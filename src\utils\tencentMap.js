// 腾讯地图工具类
class TencentMapUtils {
  constructor(apiKey) {
    this.apiKey = apiKey
    this.baseUrl = 'https://apis.map.qq.com'
  }

  // 地点搜索
  async searchPlace(keyword, region = '全国', pageSize = 10) {
    try {
      const url = `${this.baseUrl}/ws/place/v1/search`
      const params = new URLSearchParams({
        boundary: `region(${region},0)`,
        keyword: keyword,
        page_size: pageSize,
        page_index: 1,
        orderby: '_distance',
        key: this.apiKey
      })

      const response = await fetch(`${url}?${params}`)
      const data = await response.json()

      if (data.status === 0) {
        return data.data.map(item => ({
          id: item.id,
          title: item.title,
          address: item.address,
          location: item.location,
          category: item.category
        }))
      } else {
        throw new Error(data.message || '搜索失败')
      }
    } catch (error) {
      console.error('地点搜索失败:', error)
      throw error
    }
  }

  // 逆地理编码（坐标转地址）
  async reverseGeocode(lat, lng) {
    try {
      const url = `${this.baseUrl}/ws/geocoder/v1/`
      const params = new URLSearchParams({
        location: `${lat},${lng}`,
        key: this.apiKey,
        get_poi: 1
      })

      const response = await fetch(`${url}?${params}`)
      const data = await response.json()

      if (data.status === 0) {
        return {
          address: data.result.address,
          formatted_addresses: data.result.formatted_addresses,
          address_component: data.result.address_component,
          pois: data.result.pois
        }
      } else {
        throw new Error(data.message || '逆地理编码失败')
      }
    } catch (error) {
      console.error('逆地理编码失败:', error)
      throw error
    }
  }

  // 地理编码（地址转坐标）
  async geocode(address, region = '') {
    try {
      const url = `${this.baseUrl}/ws/geocoder/v1/`
      const params = new URLSearchParams({
        address: address,
        key: this.apiKey
      })

      if (region) {
        params.append('region', region)
      }

      const response = await fetch(`${url}?${params}`)
      const data = await response.json()

      if (data.status === 0) {
        return {
          location: data.result.location,
          similarity: data.result.similarity,
          deviation: data.result.deviation,
          reliability: data.result.reliability
        }
      } else {
        throw new Error(data.message || '地理编码失败')
      }
    } catch (error) {
      console.error('地理编码失败:', error)
      throw error
    }
  }

  // 路线规划
  async getRoute(from, to, mode = 'driving') {
    try {
      const url = `${this.baseUrl}/ws/direction/v1/${mode}/`
      const params = new URLSearchParams({
        from: `${from.lat},${from.lng}`,
        to: `${to.lat},${to.lng}`,
        key: this.apiKey
      })

      const response = await fetch(`${url}?${params}`)
      const data = await response.json()

      if (data.status === 0) {
        return {
          routes: data.result.routes,
          distance: data.result.routes[0]?.distance,
          duration: data.result.routes[0]?.duration
        }
      } else {
        throw new Error(data.message || '路线规划失败')
      }
    } catch (error) {
      console.error('路线规划失败:', error)
      throw error
    }
  }

  // 周边搜索
  async searchNearby(lat, lng, keyword = '', radius = 1000) {
    try {
      const url = `${this.baseUrl}/ws/place/v1/search`
      const params = new URLSearchParams({
        boundary: `nearby(${lat},${lng},${radius})`,
        keyword: keyword,
        page_size: 20,
        page_index: 1,
        orderby: '_distance',
        key: this.apiKey
      })

      const response = await fetch(`${url}?${params}`)
      const data = await response.json()

      if (data.status === 0) {
        return data.data.map(item => ({
          id: item.id,
          title: item.title,
          address: item.address,
          location: item.location,
          category: item.category,
          distance: item._distance
        }))
      } else {
        throw new Error(data.message || '周边搜索失败')
      }
    } catch (error) {
      console.error('周边搜索失败:', error)
      throw error
    }
  }

  // 生成静态地图URL
  generateStaticMapUrl(center, zoom = 10, size = '400x300', markers = []) {
    const url = `${this.baseUrl}/ws/staticmap/v2/`
    const params = new URLSearchParams({
      center: `${center.lat},${center.lng}`,
      zoom: zoom,
      size: size,
      key: this.apiKey
    })

    if (markers.length > 0) {
      const markerStr = markers.map(marker => 
        `${marker.lat},${marker.lng}`
      ).join('|')
      params.append('markers', markerStr)
    }

    return `${url}?${params}`
  }
}

// 创建实例并导出
const tencentMapUtils = new TencentMapUtils('NGOBZ-JGBOM-GPT64-6SXGY-ZPZLQ-TAFAG')

export default tencentMapUtils
export { TencentMapUtils }
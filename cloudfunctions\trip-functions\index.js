const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command
const $ = db.command.aggregate

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event
  const wxContext = cloud.getWXContext()
  const openId = wxContext.OPENID

  switch (action) {
    case 'createTrip':
      return await createTrip(data, openId)
    case 'updateTrip':
      return await updateTrip(data, openId)
    case 'deleteTrip':
      return await deleteTrip(data, openId)
    case 'getTripList':
      return await getTripList(data, openId)
    case 'getTripDetail':
      return await getTripDetail(data)
    default:
      return {
        code: 404,
        message: '未找到对应的操作'
      }
  }
}

/**
 * 创建旅行计划
 */
async function createTrip(data, openId) {
  try {
    const { title, description, startDate, endDate, destination, coverImage } = data
    
    const tripData = {
      title,
      description,
      startDate,
      endDate,
      destination,
      coverImage: coverImage || '',
      diaryCount: 0,
      _openid: openId,
      createdAt: db.serverDate(),
      updatedAt: db.serverDate()
    }

    const result = await db.collection('trips').add({
      data: tripData
    })

    return {
      code: 200,
      data: {
        _id: result._id,
        ...tripData
      }
    }
  } catch (err) {
    console.error('创建旅行计划失败:', err)
    return {
      code: 500,
      message: '创建旅行计划失败'
    }
  }
}

/**
 * 更新旅行计划
 */
async function updateTrip(data, openId) {
  try {
    const { tripId, title, description, startDate, endDate, destination, coverImage } = data
    
    // 检查是否是旅行计划的创建者
    const trip = await db.collection('trips').doc(tripId).get()
    if (trip.data._openid !== openId) {
      return {
        code: 403,
        message: '无权限修改此旅行计划'
      }
    }

    const updateData = {
      updatedAt: db.serverDate()
    }

    if (title !== undefined) updateData.title = title
    if (description !== undefined) updateData.description = description
    if (startDate !== undefined) updateData.startDate = startDate
    if (endDate !== undefined) updateData.endDate = endDate
    if (destination !== undefined) updateData.destination = destination
    if (coverImage !== undefined) updateData.coverImage = coverImage

    await db.collection('trips').doc(tripId).update({
      data: updateData
    })

    return {
      code: 200,
      message: '更新旅行计划成功'
    }
  } catch (err) {
    console.error('更新旅行计划失败:', err)
    return {
      code: 500,
      message: '更新旅行计划失败'
    }
  }
}

/**
 * 删除旅行计划
 */
async function deleteTrip(data, openId) {
  try {
    const { tripId } = data
    
    // 检查是否是旅行计划的创建者
    const trip = await db.collection('trips').doc(tripId).get()
    if (trip.data._openid !== openId) {
      return {
        code: 403,
        message: '无权限删除此旅行计划'
      }
    }

    // 删除关联的日记
    const { data: diaries } = await db.collection('diaries').where({
      tripId
    }).get()
    
    const diaryIds = diaries.map(diary => diary._id)
    
    // 删除日记相关的评论
    if (diaryIds.length > 0) {
      await db.collection('comments').where({
        diaryId: _.in(diaryIds)
      }).remove()
    }
    
    // 删除日记
    await db.collection('diaries').where({
      tripId
    }).remove()

    // 删除旅行计划
    await db.collection('trips').doc(tripId).remove()

    return {
      code: 200,
      message: '删除旅行计划成功'
    }
  } catch (err) {
    console.error('删除旅行计划失败:', err)
    return {
      code: 500,
      message: '删除旅行计划失败'
    }
  }
}

/**
 * 获取旅行计划列表
 */
async function getTripList(data, openId) {
  try {
    const { userId, page = 1, pageSize = 10 } = data
    const skip = (page - 1) * pageSize
    
    let query = db.collection('trips')
    
    // 根据用户ID筛选
    if (userId) {
      query = query.where({
        _openid: userId
      })
    }
    
    // 获取总数
    const countResult = await query.count()
    
    // 获取分页数据
    const { data: trips } = await query
      .orderBy('createdAt', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get()
    
    // 获取每个旅行计划的作者信息
    const userIds = [...new Set(trips.map(trip => trip._openid))]
    const { data: users } = await db.collection('users')
      .where({
        _openid: _.in(userIds)
      })
      .get()
    
    const userMap = {}
    users.forEach(user => {
      userMap[user._openid] = user
    })
    
    const result = trips.map(trip => {
      const user = userMap[trip._openid] || {}
      return {
        ...trip,
        author: {
          _id: user._id,
          name: user.name,
          avatar: user.avatar
        }
      }
    })
    
    return {
      code: 200,
      data: {
        list: result,
        total: countResult.total,
        page,
        pageSize
      }
    }
  } catch (err) {
    console.error('获取旅行计划列表失败:', err)
    return {
      code: 500,
      message: '获取旅行计划列表失败'
    }
  }
}

/**
 * 获取旅行计划详情
 */
async function getTripDetail(data) {
  try {
    const { tripId } = data
    
    const { data: trip } = await db.collection('trips').doc(tripId).get()
    
    if (!trip) {
      return {
        code: 404,
        message: '旅行计划不存在'
      }
    }
    
    // 获取作者信息
    const { data: users } = await db.collection('users')
      .where({
        _openid: trip._openid
      })
      .get()
    
    const author = users[0] || {}
    
    // 获取关联的日记列表
    const { data: diaries } = await db.collection('diaries')
      .where({
        tripId
      })
      .orderBy('createdAt', 'desc')
      .limit(5)
      .get()
    
    return {
      code: 200,
      data: {
        ...trip,
        author: {
          _id: author._id,
          name: author.name,
          avatar: author.avatar
        },
        recentDiaries: diaries
      }
    }
  } catch (err) {
    console.error('获取旅行计划详情失败:', err)
    return {
      code: 500,
      message: '获取旅行计划详情失败'
    }
  }
}
<template>
  <div class="login-container">
    <h2>欢迎使用旅行日记</h2>
    
    <div class="login-form">
      <div class="form-group">
        <label>邮箱</label>
        <input v-model="email" type="email" placeholder="请输入邮箱">
      </div>
      
      <div class="form-group">
        <label>密码</label>
        <input v-model="password" type="password" placeholder="请输入密码">
      </div>
      
      <button @click="handleLogin" class="login-btn">登录</button>
      <button @click="handleAnonymousLogin" class="anonymous-btn">游客体验</button>
    </div>
  </div>
</template>

<script>
import { auth } from '../utils/auth';

export default {
  data() {
    return {
      email: '',
      password: ''
    }
  },
  methods: {
    async handleLogin() {
      try {
        await auth.emailLogin(this.email, this.password);
        this.$router.push('/diaries');
      } catch (error) {
        alert('登录失败: ' + error.message);
      }
    },
    async handleAnonymousLogin() {
      try {
        await auth.anonymousLogin();
        this.$router.push('/diaries');
      } catch (error) {
        alert('匿名登录失败: ' + error.message);
      }
    }
  }
}
</script>

<style scoped>
.login-container {
  max-width: 400px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.login-form {
  margin-top: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
}

.form-group input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.login-btn, .anonymous-btn {
  width: 100%;
  padding: 0.75rem;
  margin-top: 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.login-btn {
  background-color: #42b983;
  color: white;
}

.anonymous-btn {
  background-color: #f0f0f0;
}
</style>
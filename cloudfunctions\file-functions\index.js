const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event
  const wxContext = cloud.getWXContext()
  const openId = wxContext.OPENID

  switch (action) {
    case 'getUploadToken':
      return await getUploadToken(data, openId)
    case 'getDownloadUrl':
      return await getDownloadUrl(data)
    case 'deleteFile':
      return await deleteFile(data, openId)
    default:
      return {
        code: 404,
        message: '未找到对应的操作'
      }
  }
}

/**
 * 获取文件上传凭证
 */
async function getUploadToken(data, openId) {
  try {
    const { fileName, fileType } = data
    
    // 生成唯一的文件ID
    const fileId = `user_${openId}_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`
    
    // 获取上传配置
    const result = await cloud.getTempFileURL({
      cloudPath: `uploads/${fileId}.${fileType || 'jpg'}`,
      maxAge: 60 * 60 // 1小时有效期
    })

    return {
      code: 200,
      data: {
        uploadUrl: result.url,
        fileId,
        expiredTime: Date.now() + 60 * 60 * 1000 // 1小时后过期
      }
    }
  } catch (err) {
    console.error('获取上传凭证失败:', err)
    return {
      code: 500,
      message: '获取上传凭证失败'
    }
  }
}

/**
 * 获取文件下载地址
 */
async function getDownloadUrl(data) {
  try {
    const { fileId } = data
    
    // 获取文件下载地址
    const result = await cloud.getTempFileURL({
      fileList: [{
        fileID: fileId,
        maxAge: 60 * 60 * 24 // 24小时有效期
      }]
    })

    if (result.fileList.length === 0 || !result.fileList[0].tempFileURL) {
      return {
        code: 404,
        message: '文件不存在'
      }
    }

    return {
      code: 200,
      data: {
        downloadUrl: result.fileList[0].tempFileURL,
        expiredTime: Date.now() + 60 * 60 * 24 * 1000 // 24小时后过期
      }
    }
  } catch (err) {
    console.error('获取下载地址失败:', err)
    return {
      code: 500,
      message: '获取下载地址失败'
    }
  }
}

/**
 * 删除文件
 */
async function deleteFile(data, openId) {
  try {
    const { fileId } = data
    
    // 检查文件是否存在
    const result = await cloud.getTempFileURL({
      fileList: [fileId]
    })

    if (result.fileList.length === 0 || !result.fileList[0].tempFileURL) {
      return {
        code: 404,
        message: '文件不存在'
      }
    }

    // 检查文件是否属于当前用户
    if (!fileId.startsWith(`user_${openId}_`)) {
      return {
        code: 403,
        message: '无权限删除此文件'
      }
    }

    // 删除文件
    await cloud.deleteFile({
      fileList: [fileId]
    })

    return {
      code: 200,
      message: '删除文件成功'
    }
  } catch (err) {
    console.error('删除文件失败:', err)
    return {
      code: 500,
      message: '删除文件失败'
    }
  }
}
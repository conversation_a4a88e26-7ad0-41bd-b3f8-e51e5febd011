import { createRouter, createWebHashHistory } from 'vue-router'
import Home from '@/pages/Home/index.vue'
import { checkLogin } from '@/utils/cloudbase'
import MapPage from '@/pages/MapPage.vue'
import DiaryEdit from '@/pages/DiaryEdit/index.vue'
import TimelinePage from '@/pages/TimelinePage.vue'
import StatsPage from '@/pages/StatsPage.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { requiresAuth: false }
  },
  {
    path: '/diary/:id',
    name: 'DiaryDetail',
    component: () => import('@/pages/DiaryDetail/index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/pages/Profile/index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/auth',
    name: 'Auth',
    component: () => import('@/pages/Auth/index.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/diary/edit/:id?',
    name: 'DiaryEdit',
    component: DiaryEdit,
    meta: { requiresAuth: true }
  },
  { path: '/map', component: MapPage },
  { path: '/timeline', component: TimelinePage },
  { path: '/stats', component: StatsPage },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  try {
    const loginState = await checkLogin()
    const isAuthenticated = loginState && loginState.isLoggedIn
    
    if (to.meta.requiresAuth && !isAuthenticated) {
      next('/auth')
    } else {
      next()
    }
  } catch (error) {
    console.error('登录检查失败:', error)
    next('/auth')
  }
})

export default router
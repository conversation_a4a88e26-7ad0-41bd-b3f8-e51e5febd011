<template>
  <div class="min-h-screen bg-gray-50">
    <NavBar :title="diary?.title || '日记详情'" :showBack="true" />
    
    <main class="container mx-auto px-4 py-8">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>

      <!-- 错误提示 -->
      <div v-if="error" class="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-red-700">{{ error }}</p>
          </div>
        </div>
      </div>

      <!-- 日记内容 -->
      <div v-if="diary && !loading" class="bg-white shadow rounded-lg overflow-hidden">
        <!-- 封面图 -->
        <div v-if="diary.coverImage" class="w-full h-64 bg-gray-200">
          <img :src="diary.coverImage" alt="封面图" class="w-full h-full object-cover">
        </div>

        <!-- 日记内容 -->
        <div class="p-6">
          <div class="flex justify-between items-center mb-4">
            <h1 class="text-2xl font-bold text-gray-900">{{ diary.title }}</h1>
            <span class="text-sm text-gray-500">{{ formatDate(diary.createdAt) }}</span>
          </div>

          <div class="prose max-w-none">
            <p class="whitespace-pre-line">{{ diary.content }}</p>
          </div>

          <!-- 位置信息 -->
          <div v-if="diary.location" class="mt-6 flex items-center text-gray-600">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span>{{ diary.location }}</span>
          </div>

          <!-- 标签 -->
          <div v-if="diary.tags && diary.tags.length > 0" class="mt-4 flex flex-wrap gap-2">
            <span 
              v-for="tag in diary.tags" 
              :key="tag"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
            >
              {{ tag }}
            </span>
          </div>

          <!-- 操作按钮 -->
          <div class="mt-8 flex justify-end space-x-4">
            <button 
              @click="editDiary" 
              class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
              </svg>
              编辑
            </button>
            <button 
              @click="confirmDelete" 
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
              删除
            </button>
          </div>
        </div>
      </div>

      <!-- 删除确认对话框 -->
      <div v-if="showDeleteConfirm" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-sm mx-auto">
          <h3 class="text-lg font-medium text-gray-900">确认删除</h3>
          <p class="mt-2 text-sm text-gray-500">
            确定要删除这篇日记吗？此操作无法撤销。
          </p>
          <div class="mt-4 flex justify-end space-x-3">
            <button 
              @click="showDeleteConfirm = false" 
              class="inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              取消
            </button>
            <button 
              @click="deleteDiary" 
              class="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              删除
            </button>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { db } from '@/utils/cloudbase'
import NavBar from '@/components/NavBar/NavBar.vue'

export default {
  components: {
    NavBar
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const diary = ref(null)
    const loading = ref(true)
    const error = ref(null)
    const showDeleteConfirm = ref(false)

    const diaryId = route.params.id

    // 获取日记详情
    const fetchDiary = async () => {
      if (diaryId === 'new') {
        // 处理新建日记的情况
        router.push('/diary/edit/new')
        return
      }

      try {
        loading.value = true
        error.value = null
        
        // 这里需要实现获取单个日记的方法
        // 临时使用模拟数据
        diary.value = {
          _id: diaryId,
          title: '示例日记标题',
          content: '这是一篇示例日记的内容。实际应用中，这里会显示从数据库获取的真实日记内容。',
          createdAt: new Date().toISOString(),
          location: '北京市海淀区',
          tags: ['旅行', '美食']
        }
        
        // 实际应用中应该使用类似下面的代码
        // const result = await db.collection('diaries').doc(diaryId).get()
        // if (result.data.length === 0) {
        //   error.value = '未找到该日记'
        //   return
        // }
        // diary.value = result.data[0]
      } catch (err) {
        error.value = '加载日记失败: ' + err.message
      } finally {
        loading.value = false
      }
    }

    // 格式化日期
    const formatDate = (timestamp) => {
      return new Date(timestamp).toLocaleDateString()
    }

    // 编辑日记
    const editDiary = () => {
      router.push(`/diary/edit/${diaryId}`)
    }

    // 确认删除
    const confirmDelete = () => {
      showDeleteConfirm.value = true
    }

    // 删除日记
    const deleteDiary = async () => {
      try {
        // 实际应用中应该使用类似下面的代码
        // await db.collection('diaries').doc(diaryId).remove()
        
        showDeleteConfirm.value = false
        router.push('/')
      } catch (err) {
        error.value = '删除日记失败: ' + err.message
        showDeleteConfirm.value = false
      }
    }

    onMounted(fetchDiary)

    return {
      diary,
      loading,
      error,
      showDeleteConfirm,
      formatDate,
      editDiary,
      confirmDelete,
      deleteDiary
    }
  }
}
</script>
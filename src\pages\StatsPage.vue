<template>
  <div class="min-h-screen bg-gray-50 p-4">
    <h1 class="text-2xl font-bold mb-4">旅行统计</h1>
    <p>访问地点数: {{ stats.locations }}</p>
    <p>总日记数: {{ stats.diaries }}</p>
    <button @click="share" class="bg-green-500 text-white p-2 mt-4">分享行程</button>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { db } from '@/utils/cloudbase'

const stats = ref({ locations: 0, diaries: 0 })

onMounted(async () => {
  const locRes = await db.collection('locations').count()
  const diaryRes = await db.collection('diaries').count()
  stats.value = { locations: locRes.total, diaries: diaryRes.total }
})

const share = () => {
  // TODO: 生成分享链接
  alert('分享功能待实现')
}
</script> 
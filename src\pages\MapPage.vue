<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 调试信息 -->
    <div v-if="debugMode" class="fixed top-0 left-0 right-0 bg-yellow-100 p-2 z-50 text-xs">
      <div>路由: {{ $route.path }}</div>
      <div>组件已挂载: {{ mounted }}</div>
      <div>标记数量: {{ markers.length }}</div>
    </div>
    
    <!-- 导航栏 -->
    <NavBar 
      title="旅行地图" 
      :show-search="true"
      :show-add="true"
      @search="handleSearch"
      @add="handleAddMarker"
    />
    
    <!-- 地图容器 -->
    <div class="h-[calc(100vh-4rem)]">
      <InteractiveMap
        :initial-center="mapCenter"
        :initial-zoom="mapZoom"
        @marker-added="handleMarkerAdded"
        @marker-updated="handleMarkerUpdated"
        @marker-deleted="handleMarkerDeleted"
      />
    </div>
    
    <!-- 统计信息浮层 -->
    <div class="absolute top-20 left-4 bg-white rounded-lg shadow-lg p-3 z-10">
      <div class="text-sm text-gray-600">
        <div class="flex items-center space-x-2 mb-1">
          <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
          <span>已标记地点: {{ totalMarkers }}</span>
        </div>
        <div class="flex items-center space-x-2 mb-1">
          <span class="w-2 h-2 bg-green-500 rounded-full"></span>
          <span>本月新增: {{ monthlyMarkers }}</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
          <span>总距离: {{ totalDistance }}km</span>
        </div>
      </div>
    </div>
    
    <!-- 快速操作面板 -->
    <div class="absolute bottom-24 left-4 bg-white rounded-lg shadow-lg p-3 z-10">
      <div class="text-sm font-medium text-gray-700 mb-2">快速操作</div>
      <div class="flex flex-col space-y-2">
        <button 
          @click="findNearbyFood"
          class="flex items-center space-x-2 px-3 py-2 text-sm bg-orange-50 text-orange-700 rounded-lg hover:bg-orange-100 transition-colors"
        >
          <span>🍽️</span>
          <span>附近美食</span>
        </button>
        <button 
          @click="findNearbyHotels"
          class="flex items-center space-x-2 px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors"
        >
          <span>🏨</span>
          <span>附近酒店</span>
        </button>
        <button 
          @click="findNearbyAttractions"
          class="flex items-center space-x-2 px-3 py-2 text-sm bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors"
        >
          <span>🎯</span>
          <span>附近景点</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import NavBar from '@/components/NavBar/NavBar.vue'
import InteractiveMap from '@/components/Map/InteractiveMap.vue'
import { db } from '@/utils/cloudbase'
import tencentMapUtils from '@/utils/tencentMap'

export default {
  name: 'MapPage',
  components: {
    NavBar,
    InteractiveMap
  },
  setup() {
    const router = useRouter()
    const mounted = ref(false)
    const debugMode = ref(process.env.NODE_ENV === 'development')
    const markers = ref([])
    const mapCenter = ref({ lat: 39.9042, lng: 116.4074 }) // 默认北京
    const mapZoom = ref(11)
    
    // 计算属性
    const totalMarkers = computed(() => markers.value.length)
    
    const monthlyMarkers = computed(() => {
      const now = new Date()
      const currentMonth = now.getMonth()
      const currentYear = now.getFullYear()
      
      return markers.value.filter(marker => {
        const markerDate = new Date(marker.createdAt)
        return markerDate.getMonth() === currentMonth && 
               markerDate.getFullYear() === currentYear
      }).length
    })
    
    const totalDistance = computed(() => {
      // 计算所有标记点之间的总距离（简化计算）
      if (markers.value.length < 2) return 0
      
      let distance = 0
      for (let i = 1; i < markers.value.length; i++) {
        const prev = markers.value[i - 1]
        const curr = markers.value[i]
        distance += calculateDistance(prev.lat, prev.lng, curr.lat, curr.lng)
      }
      
      return Math.round(distance)
    })
    
    // 计算两点间距离（简化版）
    const calculateDistance = (lat1, lng1, lat2, lng2) => {
      const R = 6371 // 地球半径（公里）
      const dLat = (lat2 - lat1) * Math.PI / 180
      const dLng = (lng2 - lng1) * Math.PI / 180
      const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLng / 2) * Math.sin(dLng / 2)
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
      return R * c
    }
    
    // 加载标记数据
    const loadMarkers = async () => {
      try {
        const result = await db.collection('locations').get()
        markers.value = result.data
      } catch (error) {
        console.error('加载标记数据失败:', error)
      }
    }
    
    // 处理搜索
    const handleSearch = () => {
      // 可以打开搜索面板或其他搜索逻辑
      console.log('打开搜索')
    }
    
    // 处理添加标记
    const handleAddMarker = () => {
      // 触发地图添加模式
      console.log('添加标记模式')
    }
    
    // 查找附近美食
    const findNearbyFood = async () => {
      try {
        const center = mapCenter.value
        const results = await tencentMapUtils.searchNearby(
          center.lat, 
          center.lng, 
          '美食', 
          2000
        )
        console.log('附近美食:', results)
        // 这里可以在地图上显示结果
      } catch (error) {
        console.error('搜索附近美食失败:', error)
      }
    }
    
    // 查找附近酒店
    const findNearbyHotels = async () => {
      try {
        const center = mapCenter.value
        const results = await tencentMapUtils.searchNearby(
          center.lat, 
          center.lng, 
          '酒店', 
          2000
        )
        console.log('附近酒店:', results)
      } catch (error) {
        console.error('搜索附近酒店失败:', error)
      }
    }
    
    // 查找附近景点
    const findNearbyAttractions = async () => {
      try {
        const center = mapCenter.value
        const results = await tencentMapUtils.searchNearby(
          center.lat, 
          center.lng, 
          '景点', 
          5000
        )
        console.log('附近景点:', results)
      } catch (error) {
        console.error('搜索附近景点失败:', error)
      }
    }
    
    // 标记事件处理
    const handleMarkerAdded = (marker) => {
      markers.value.push(marker)
      console.log('新增标记:', marker)
    }
    
    const handleMarkerUpdated = (marker) => {
      const index = markers.value.findIndex(m => m._id === marker._id)
      if (index > -1) {
        markers.value[index] = marker
      }
      console.log('更新标记:', marker)
    }
    
    const handleMarkerDeleted = (marker) => {
      const index = markers.value.findIndex(m => m._id === marker._id)
      if (index > -1) {
        markers.value.splice(index, 1)
      }
      console.log('删除标记:', marker)
    }
    
    // 组件挂载时加载数据
    onMounted(async () => {
      try {
        mounted.value = true
        console.log('MapPage 组件已挂载')
        await loadMarkers()
      } catch (error) {
        console.error('MapPage 初始化失败:', error)
      }
    })
    
    return {
      mounted,
      debugMode,
      markers,
      mapCenter,
      mapZoom,
      totalMarkers,
      monthlyMarkers,
      totalDistance,
      handleSearch,
      handleAddMarker,
      handleMarkerAdded,
      handleMarkerUpdated,
      handleMarkerDeleted,
      findNearbyFood,
      findNearbyHotels,
      findNearbyAttractions
    }
  }
}
</script>
const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command
const $ = db.command.aggregate

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, diaryId, data } = event
  const wxContext = cloud.getWXContext()
  const openId = wxContext.OPENID

  switch (action) {
    case 'create':
      return await createDiary(data, openId)
    case 'update':
      return await updateDiary({ diaryId, ...data }, openId)
    case 'get':
      return await getDiaryDetail({ diaryId })
    case 'createDiary':
      return await createDiary(data, openId)
    case 'updateDiary':
      return await updateDiary(data, openId)
    case 'deleteDiary':
      return await deleteDiary(data, openId)
    case 'getDiaryList':
      return await getDiaryList(data, openId)
    case 'getDiaryDetail':
      return await getDiaryDetail(data)
    default:
      return {
        code: 404,
        message: '未找到对应的操作'
      }
  }
}

/**
 * 创建日记
 */
async function createDiary(data, openId) {
  try {
    const { title, content, images } = data
    
    const diaryData = {
      title,
      content,
      images: images || [],
      _openid: openId,
      createdAt: db.serverDate(),
      updatedAt: db.serverDate()
    }

    const result = await db.collection('diaries').add({
      data: diaryData
    })

    return {
      code: 200,
      data: {
        id: result._id,
        ...diaryData
      }
    }
  } catch (err) {
    console.error('创建日记失败:', err)
    return {
      code: 500,
      message: '创建日记失败'
    }
  }
}

/**
 * 更新日记
 */
async function updateDiary(data, openId) {
  try {
    const { diaryId, title, content, images } = data
    
    // 检查是否是日记的创建者
    const diary = await db.collection('diaries').doc(diaryId).get()
    if (diary.data._openid !== openId) {
      return {
        code: 403,
        message: '无权限修改此日记'
      }
    }

    const updateData = {
      updatedAt: db.serverDate()
    }

    if (title !== undefined) updateData.title = title
    if (content !== undefined) updateData.content = content
    if (images !== undefined) updateData.images = images

    await db.collection('diaries').doc(diaryId).update({
      data: updateData
    })

    return {
      code: 200,
      data: {
        id: diaryId
      }
    }
  } catch (err) {
    console.error('更新日记失败:', err)
    return {
      code: 500,
      message: '更新日记失败'
    }
  }
}

/**
 * 获取日记详情
 */
async function getDiaryDetail(data) {
  try {
    const { diaryId } = data
    
    const { data: diary } = await db.collection('diaries').doc(diaryId).get()
    
    if (!diary) {
      return {
        code: 404,
        message: '日记不存在'
      }
    }
    
    return {
      code: 200,
      data: diary
    }
  } catch (err) {
    console.error('获取日记详情失败:', err)
    return {
      code: 500,
      message: '获取日记详情失败'
    }
  }
}

// 保留其他函数实现...
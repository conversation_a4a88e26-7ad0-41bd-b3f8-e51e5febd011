<template>
  <header class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 py-3 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between">
        <!-- 左侧：返回按钮和标题 -->
        <div class="flex items-center space-x-3">
          <button 
            v-if="showBack" 
            @click="$router.go(-1)"
            class="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 class="text-xl font-semibold text-gray-900">{{ title }}</h1>
        </div>

        <!-- 右侧：操作按钮 -->
        <div class="flex items-center space-x-2">
          <!-- 搜索按钮 -->
          <button 
            v-if="showSearch"
            @click="$emit('search')"
            class="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button>
          
          <!-- 添加按钮 -->
          <button 
            v-if="showAdd"
            @click="$emit('add')"
            class="p-2 rounded-lg bg-blue-500 text-white hover:bg-blue-600 transition-colors"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
          </button>
          
          <!-- 用户菜单 -->
          <div class="relative">
            <button 
              @click="toggleUserMenu"
              class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                <span class="text-white text-sm font-medium">{{ userInitial }}</span>
              </div>
            </button>
            
            <!-- 用户菜单下拉 -->
            <div 
              v-if="showUserMenu" 
              class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50"
            >
              <router-link 
                to="/profile" 
                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                @click="showUserMenu = false"
              >
                个人资料
              </router-link>
              <router-link 
                to="/stats" 
                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                @click="showUserMenu = false"
              >
                统计数据
              </router-link>
              <hr class="my-1">
              <button 
                @click="logout"
                class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'NavBar',
  props: {
    title: {
      type: String,
      default: '旅行日记'
    },
    showBack: {
      type: Boolean,
      default: false
    },
    showSearch: {
      type: Boolean,
      default: false
    },
    showAdd: {
      type: Boolean,
      default: false
    }
  },
  emits: ['search', 'add'],
  setup() {
    const router = useRouter()
    const showUserMenu = ref(false)
    
    const userInitial = computed(() => {
      // 这里可以从用户状态获取真实的用户名首字母
      return '游'
    })
    
    const toggleUserMenu = () => {
      showUserMenu.value = !showUserMenu.value
    }
    
    const logout = () => {
      // 处理退出登录逻辑
      console.log('退出登录')
      showUserMenu.value = false
      router.push('/auth')
    }
    
    return {
      showUserMenu,
      userInitial,
      toggleUserMenu,
      logout
    }
  }
}
</script>
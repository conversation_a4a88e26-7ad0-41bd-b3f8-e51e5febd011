<template>
  <div class="relative w-full h-full">
    <!-- 永久存在的容器 -->
    <div ref="mapContainer" class="w-full h-full"></div>

    <!-- 加载状态覆盖层 -->
    <!-- 修改加载状态 -->
    <!-- 修改加载状态 -->
    <!-- 修改加载状态 -->
    <div v-if="loading" class="absolute inset-0 bg-white/95 z-50 flex items-center justify-center">
      <div class="text-center space-y-4">
        <div class="relative w-20 h-20 mx-auto">
          <div class="absolute inset-0 animate-ping bg-blue-400 rounded-full"></div>
          <div class="absolute inset-2 bg-blue-500 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-white animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </div>
        </div>
        <p class="text-gray-600 font-medium">地图加载中...</p>
      </div>
    </div>
    <!-- 错误状态覆盖层 -->
    <div v-if="error" class="absolute inset-0 bg-white/90 z-50 flex items-center justify-center">
      <div class="text-center">
        <div class="text-red-500 mb-4">
          <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">地图加载失败</h3>
        <p class="text-gray-600 mb-4">{{ error }}</p>
        <button @click="initMap" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
          重新加载
        </button>
      </div>
    </div>

    <!-- 地图控件容器 -->
    <div v-show="!loading && !error" class="absolute inset-0">
      <!-- 修改搜索框容器 -->
      <div class="absolute top-4 left-4 right-4 z-10 transition-all duration-300">
        <div class="bg-white rounded-xl shadow-xl p-3 backdrop-blur-sm bg-opacity-90">
          // ... existing code ... (搜索框、按钮、弹窗、表单等所有 v-else 中的内容，除了地图容器)
        </div>
      </div>
      
      <!-- 修改控制按钮 -->
      <div class="absolute bottom-24 right-4 z-10 flex flex-col space-y-3">
        <button 
          v-for="(btn, index) in controls" 
          :key="index"
          class="p-3 bg-white/90 backdrop-blur-sm rounded-xl shadow-xl hover:scale-105 transition-all duration-300"
          :class="{
            'bg-red-500/90 text-white': btn.active,
            'hover:shadow-lg': !btn.active
          }"
        >
          // ... 按钮内容
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { db } from '@/utils/cloudbase'

export default {
  name: 'InteractiveMap',
  props: {
    height: {
      type: String,
      default: '100%'
    },
    initialCenter: {
      type: Object,
      default: () => ({ lat: 39.9042, lng: 116.4074 }) // 北京
    },
    initialZoom: {
      type: Number,
      default: 11
    }
  },
  emits: ['marker-added', 'marker-updated', 'marker-deleted'],
  setup(props, { emit }) {
    const mapContainer = ref(null)
    const loading = ref(true);
    const error = ref(null);
    let map = null
    let geocoder = null
    
    // 腾讯地图API密钥
    const TENCENT_MAP_KEY = 'NGOBZ-JGBOM-GPT64-6SXGY-ZPZLQ-TAFAG'
    
    // 响应式数据
    const searchQuery = ref('')
    const searchResults = ref([])
    const locating = ref(false)
    const addMode = ref(false)
    const routeMode = ref(false)
    const selectedMarker = ref(null)
    const showAddForm = ref(false)
    const currentMapType = ref('vector')
    const markers = ref([])
    const mapMarkers = ref([])
    
    // 新标记数据
    const newMarker = ref({
      name: '',
      description: '',
      type: 'travel',
      lat: null,
      lng: null
    })
    
    // 标记图标配置
    const markerIcons = {
      travel: '🧳',
      food: '🍽️',
      hotel: '🏨',
      attraction: '🎯',
      other: '📍'
    }
    
    // 动态加载腾讯地图API
    function loadTencentMapAPI() {
        return new Promise((resolve, reject) => {
            if (window.qq && window.qq.maps) {
                resolve();
                return;
            }
            
            const script = document.createElement('script');
            // 修改为标准 JS API URL
            script.src = `https://map.qq.com/api/js?v=2.exp&key=${TENCENT_MAP_KEY}&callback=initTencentMap`;
            script.async = true;
            
            window.initTencentMap = () => {
                resolve();
            };
            
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
    
    // 初始化地图
    const initMap = async () => {
      try {
        loading.value = true;
        console.log('开始加载腾讯地图 API');
        await loadTencentMapAPI();
        
        // 确保 DOM 更新完成
        await nextTick(); 
        
        // 直接创建地图实例（不再需要空检查）
        map = new window.qq.maps.Map(mapContainer.value, {
          center: new window.qq.maps.LatLng(props.initialCenter.lat, props.initialCenter.lng),
          zoom: props.initialZoom
        });
        
        // 创建地理编码器
        geocoder = new window.qq.maps.Geocoder()
        
        // 地图点击事件
        window.qq.maps.event.addListener(map, 'click', handleMapClick)
        
        // 加载已有标记
        await loadMarkers()
        
        loading.value = false
        console.log('腾讯地图初始化成功')
      } catch (err) {
        console.error('地图初始化详细错误:', err);
        error.value = err.message || '地图初始化失败，请检查网络连接';
        loading.value = false
      }
    }
    
    // 加载标记数据
    const loadMarkers = async () => {
      try {
        const result = await db.collection('locations').get()
        markers.value = result.data
        
        // 清除现有标记
        mapMarkers.value.forEach(marker => {
          marker.setMap(null)
        })
        mapMarkers.value = []
        
        // 添加标记到地图
        markers.value.forEach(marker => {
          addMarkerToMap(marker)
        })
      } catch (error) {
        console.error('加载标记失败:', error)
      }
    }
    
    // 添加标记到地图
    const addMarkerToMap = (markerData) => {
      const position = new window.qq.maps.LatLng(markerData.lat, markerData.lng)
      
      // 创建自定义标记
      const marker = new window.qq.maps.Marker({
        position: position,
        map: map,
        title: markerData.name
      })
      
      // 创建信息窗口
      const infoWindow = new window.qq.maps.InfoWindow({
        map: map
      })
      
      // 标记点击事件
      window.qq.maps.event.addListener(marker, 'click', () => {
        const content = `
          <div class="p-3">
            <h3 class="font-semibold text-lg mb-2">${markerData.name}</h3>
            ${markerData.description ? `<p class="text-gray-600 mb-2">${markerData.description}</p>` : ''}
            <div class="text-sm text-gray-500">
              类型: ${getTypeLabel(markerData.type)}
            </div>
          </div>
        `
        
        infoWindow.setContent(content)
        infoWindow.setPosition(position)
        infoWindow.open()
        
        selectedMarker.value = markerData
      })
      
      mapMarkers.value.push(marker)
      return marker
    }
    
    // 获取类型标签
    const getTypeLabel = (type) => {
      const labels = {
        travel: '旅行地点',
        food: '美食',
        hotel: '住宿',
        attraction: '景点',
        other: '其他'
      }
      return labels[type] || '其他'
    }
    
    // 地图点击处理
    const handleMapClick = (event) => {
      if (addMode.value) {
        const lat = event.latLng.getLat()
        const lng = event.latLng.getLng()
        
        newMarker.value.lat = lat
        newMarker.value.lng = lng
        showAddForm.value = true
        addMode.value = false
        
        // 重置地图光标
        map.getDiv().style.cursor = ''
      }
    }
    
    // 搜索处理
    let searchTimeout = null
    const handleSearch = () => {
      clearTimeout(searchTimeout)
      searchTimeout = setTimeout(() => {
        if (searchQuery.value.length > 1) {
          searchLocation()
        } else {
          searchResults.value = []
        }
      }, 300)
    }
    
    // 搜索地点
    const searchLocation = async () => {
      if (!searchQuery.value.trim()) return
      
      try {
        // 使用腾讯地图地点搜索API
        const response = await fetch(
          `https://apis.map.qq.com/ws/place/v1/search?boundary=region(北京,0)&keyword=${encodeURIComponent(searchQuery.value)}&page_size=5&page_index=1&orderby=_distance&key=${TENCENT_MAP_KEY}`
        )
        const data = await response.json()
        
        if (data.status === 0) {
          searchResults.value = data.data.map(item => ({
            id: item.id,
            title: item.title,
            address: item.address,
            location: item.location
          }))
        }
      } catch (error) {
        console.error('搜索失败:', error)
      }
    }
    
    // 选择搜索结果
    const selectSearchResult = (result) => {
      const lat = result.location.lat
      const lng = result.location.lng
      
      map.setCenter(new window.qq.maps.LatLng(lat, lng))
      map.setZoom(15)
      
      searchResults.value = []
      searchQuery.value = result.title
    }
    
    // 获取当前位置
    const getCurrentLocation = () => {
      if (!navigator.geolocation) {
        alert('您的浏览器不支持地理定位')
        return
      }
      
      locating.value = true
      
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords
          const center = new window.qq.maps.LatLng(latitude, longitude)
          
          map.setCenter(center)
          map.setZoom(15)
          
          // 添加当前位置标记
          const marker = new window.qq.maps.Marker({
            position: center,
            map: map,
            title: '您的当前位置'
          })
          
          locating.value = false
        },
        (error) => {
          console.error('定位失败:', error)
          alert('定位失败，请检查位置权限设置')
          locating.value = false
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      )
    }
    
    // 切换添加模式
    const toggleAddMode = () => {
      addMode.value = !addMode.value
      selectedMarker.value = null
      
      if (addMode.value) {
        map.getDiv().style.cursor = 'crosshair'
      } else {
        map.getDiv().style.cursor = ''
      }
    }
    
    // 切换地图类型
    const toggleMapType = () => {
      if (currentMapType.value === 'vector') {
        map.setMapTypeId(window.qq.maps.MapTypeId.SATELLITE)
        currentMapType.value = 'satellite'
      } else {
        map.setMapTypeId(window.qq.maps.MapTypeId.ROADMAP)
        currentMapType.value = 'vector'
      }
    }
    
    // 切换路线模式
    const toggleRouteMode = () => {
      routeMode.value = !routeMode.value
      // 这里可以添加路线规划功能
    }
    
    // 保存新标记
    const saveNewMarker = async () => {
      try {
        const markerData = {
          ...newMarker.value,
          createdAt: new Date(),
          userId: 'current_user' // 这里应该从用户状态获取
        }
        
        const result = await db.collection('locations').add(markerData)
        markerData._id = result.id
        
        // 添加到本地数组
        markers.value.push(markerData)
        
        // 添加到地图
        addMarkerToMap(markerData)
        
        // 重置表单
        newMarker.value = {
          name: '',
          description: '',
          type: 'travel',
          lat: null,
          lng: null
        }
        
        showAddForm.value = false
        
        emit('marker-added', markerData)
      } catch (error) {
        console.error('保存标记失败:', error)
        alert('保存失败，请重试')
      }
    }
    
    // 取消添加标记
    const cancelAddMarker = () => {
      showAddForm.value = false
      addMode.value = false
      map.getDiv().style.cursor = ''
      newMarker.value = {
        name: '',
        description: '',
        type: 'travel',
        lat: null,
        lng: null
      }
    }
    
    // 编辑标记
    const editMarker = (marker) => {
      // 这里可以打开编辑表单
      console.log('编辑标记:', marker)
    }
    
    // 删除标记
    const deleteMarker = async (marker) => {
      if (!confirm('确定要删除这个标记吗？')) return
      
      try {
        await db.collection('locations').doc(marker._id).remove()
        
        // 从本地数组移除
        const index = markers.value.findIndex(m => m._id === marker._id)
        if (index > -1) {
          markers.value.splice(index, 1)
        }
        
        // 重新加载地图标记
        await loadMarkers()
        
        selectedMarker.value = null
        
        emit('marker-deleted', marker)
      } catch (error) {
        console.error('删除标记失败:', error)
        alert('删除失败，请重试')
      }
    }
    
    // 导航到标记
    const navigateToMarker = (marker) => {
      // 使用腾讯地图导航
      const url = `https://apis.map.qq.com/uri/v1/routeplan?type=drive&to=${marker.name}&tocoord=${marker.lat},${marker.lng}&referer=${TENCENT_MAP_KEY}`
      window.open(url, '_blank')
    }
    
    // 关闭标记详情
    const closeMarkerDetail = () => {
      selectedMarker.value = null
    }
    
    // 组件挂载
    onMounted(() => {
      initMap()
    })
    
    // 组件卸载
    onUnmounted(() => {
      if (map) {
        // 清理地图资源
        mapMarkers.value.forEach(marker => {
          marker.setMap(null)
        })
      }
    })
    
    // 修改 return 语句
    return {
      loading,
      error,
      mapContainer,
      searchQuery,
      searchResults,
      locating,
      addMode,
      routeMode,
      selectedMarker,
      showAddForm,
      newMarker,
      handleSearch,
      searchLocation,
      selectSearchResult,
      getCurrentLocation,
      toggleAddMode,
      toggleMapType,
      toggleRouteMode,
      saveNewMarker,
      cancelAddMarker,
      editMarker,
      deleteMarker,
      navigateToMarker,
      closeMarkerDetail
    }
  }
}
</script>

<style scoped>
/* 地图容器样式 */
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 搜索结果样式 */
.search-results {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
</style>

// 在 initMap 函数中添加
map.enableScrollWheelZoom(true);
map.addControl(new window.qq.maps.ZoomControl({
  position: window.qq.maps.ControlPosition.RIGHT_CENTER,
  style: {
    width: '28px',
    height: '64px',
    backgroundColor: 'white',
    borderRadius: '4px'
  }
}));
import { app } from './cloudbase';

const auth = app.auth();

/**
 * 检查登录状态
 * @returns {Promise<boolean>}
 */
export async function checkLoginState() {
  const loginState = await auth.getLoginState();
  return loginState && loginState.isLoggedIn;
}

/**
 * 获取当前用户
 * @returns {Object|null}
 */
export function getCurrentUser() {
  return auth.currentUser;
}

/**
 * 邮箱登录
 * @param {string} email 
 * @param {string} password 
 * @returns {Promise<void>}
 */
export async function emailLogin(email, password) {
  try {
    await auth.signInWithEmailAndPassword(email, password);
    return true;
  } catch (error) {
    console.error('邮箱登录失败:', error);
    throw error;
  }
}

/**
 * 匿名登录
 * @returns {Promise<void>}
 */
export async function anonymousLogin() {
  try {
    await auth.signInAnonymously();
    return true;
  } catch (error) {
    console.error('匿名登录失败:', error);
    throw error;
  }
}

/**
 * 退出登录
 * @returns {Promise<void>}
 */
export async function logout() {
  try {
    await auth.signOut();
  } catch (error) {
    console.error('退出登录失败:', error);
    throw error;
  }
}

/**
 * 跳转登录页
 * @returns {Promise<void>}
 */
export async function toDefaultLoginPage() {
  try {
    await auth.toDefaultLoginPage();
  } catch (error) {
    console.error('跳转登录页失败:', error);
    throw error;
  }
}

export const authService = {
  checkLoginState,
  getCurrentUser,
  emailLogin,
  anonymousLogin,
  logout,
  toDefaultLoginPage
};
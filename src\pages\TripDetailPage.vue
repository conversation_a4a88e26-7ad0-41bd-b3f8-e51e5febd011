<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="flex justify-center items-center h-64">
      <span class="loading loading-spinner loading-lg"></span>
    </div>
    
    <!-- 旅行计划不存在 -->
    <div v-else-if="!trip" class="text-center py-16">
      <h2 class="text-2xl font-bold mb-4">未找到旅行计划</h2>
      <p class="mb-8">该旅行计划可能已被删除或您没有权限查看</p>
      <button class="btn btn-primary" @click="goBack">返回</button>
    </div>
    
    <!-- 旅行计划详情 -->
    <div v-else>
      <!-- 返回按钮 -->
      <div class="mb-6">
        <button class="btn btn-ghost" @click="goBack">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          返回
        </button>
      </div>
      
      <!-- 旅行计划标题和操作按钮 -->
      <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h1 class="text-3xl font-bold">{{ trip.title }}</h1>
        
        <div class="flex gap-2">
          <button v-if="isOwner" class="btn btn-outline" @click="editTrip">编辑</button>
          <button v-if="isOwner" class="btn btn-error" @click="confirmDeleteTrip">删除</button>
          <button class="btn btn-primary" @click="createDiary">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            写日记
          </button>
        </div>
      </div>
      
      <!-- 旅行计划元信息 -->
      <div class="flex flex-wrap gap-4 mb-6 text-sm text-gray-500">
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          {{ formatDate(trip.startDate) }} - {{ formatDate(trip.endDate) }}
        </div>
        
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          {{ author.name || '匿名用户' }}
        </div>
        
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          {{ trip.isPublic ? '公开' : '私密' }}
        </div>
      </div>
      
      <!-- 封面图 -->
      <div v-if="trip.coverImage" class="mb-6">
        <img :src="trip.coverImage" alt="旅行计划封面" class="w-full h-64 object-cover rounded-lg shadow-md">
      </div>
      
      <!-- 旅行计划描述 -->
      <div v-if="trip.description" class="prose max-w-none mb-8">
        <h3 class="text-xl font-bold mb-4">旅行计划描述</h3>
        <p v-html="trip.description.replace(/\n/g, '<br>')"></p>
      </div>
      
      <!-- 目的地列表 -->
      <div v-if="trip.destinations && trip.destinations.length > 0" class="mb-8">
        <h3 class="text-xl font-bold mb-4">目的地</h3>
        <div class="flex flex-wrap gap-2">
          <div v-for="(destination, index) in trip.destinations" :key="index" class="badge badge-lg">
            {{ destination }}
          </div>
        </div>
      </div>
      
      <!-- 地图 -->
      <div v-if="trip.destinations && trip.destinations.length > 0" class="mb-8">
        <h3 class="text-xl font-bold mb-4">行程地图</h3>
        <div id="map" class="h-64 rounded-lg shadow-md"></div>
      </div>
      
      <!-- 相关日记 -->
      <div class="mb-8">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-bold">旅行日记 ({{ diaries.length }})</h3>
          <button class="btn btn-sm btn-primary" @click="createDiary">写日记</button>
        </div>
        
        <!-- 日记列表 -->
        <div v-if="diaries.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="diary in diaries" :key="diary._id" class="card bg-base-100 shadow-md cursor-pointer hover:shadow-lg transition-shadow" @click="viewDiaryDetail(diary._id)">
            <figure v-if="diary.coverImage">
              <img :src="diary.coverImage" alt="日记封面" class="w-full h-48 object-cover">
            </figure>
            <div class="card-body">
              <h4 class="card-title">{{ diary.title }}</h4>
              <p class="text-sm text-gray-500">{{ formatDate(diary.date) }}</p>
              <p v-if="diary.location" class="text-sm flex items-center text-gray-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                {{ diary.location }}
              </p>
              <p v-if="diary.content" class="line-clamp-2 text-gray-600">{{ diary.content }}</p>
              <div v-if="diary.tags && diary.tags.length > 0" class="flex flex-wrap gap-1 mt-2">
                <span v-for="tag in diary.tags" :key="tag" class="badge badge-sm">{{ tag }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 无日记提示 -->
        <div v-else class="text-center py-16 bg-base-200 rounded-lg">
          <h4 class="text-xl font-bold mb-2">还没有日记</h4>
          <p class="mb-6 text-gray-500">记录下你的旅行回忆吧</p>
          <button class="btn btn-primary" @click="createDiary">写第一篇日记</button>
        </div>
      </div>
    </div>
    
    <!-- 删除确认对话框 -->
    <dialog id="delete_confirm_modal" class="modal">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">确认删除</h3>
        <p>您确定要删除这个旅行计划吗？此操作无法撤销。</p>
        <p class="text-warning mt-2">注意：删除旅行计划不会删除相关的日记。</p>
        <div class="modal-action">
          <button class="btn" @click="closeDeleteModal">取消</button>
          <button class="btn btn-error" @click="deleteTrip" :disabled="isDeleting">
            <span v-if="isDeleting" class="loading loading-spinner loading-xs mr-1"></span>
            删除
          </button>
        </div>
      </div>
    </dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

export default {
  name: 'TripDetailPage',
  setup() {
    const route = useRoute();
    const router = useRouter();
    
    // 状态变量
    const isLoading = ref(true);
    const isDeleting = ref(false);
    const isLoggedIn = ref(false);
    const currentUserId = ref(null);
    const trip = ref(null);
    const author = ref({});
    const diaries = ref([]);
    
    // 计算属性
    const isOwner = computed(() => {
      return trip.value && currentUserId.value && trip.value.userId === currentUserId.value;
    });
    
    // 生命周期钩子
    onMounted(async () => {
      await checkLoginStatus();
      await fetchTripDetail();
      isLoading.value = false;
    });
    
    // 方法
    const checkLoginStatus = async () => {
      try {
        // 这里应该调用云开发的登录状态检查
        // 示例代码，实际实现需要根据云开发SDK调整
        const app = cloudbase.init({
          env: 'your-env-id' // 环境ID需要替换
        });
        const auth = app.auth();
        const loginState = await auth.getLoginState();
        
        isLoggedIn.value = loginState && loginState.isLoggedIn;
        if (isLoggedIn.value) {
          currentUserId.value = loginState.user.uid;
        }
      } catch (error) {
        console.error('检查登录状态失败:', error);
        isLoggedIn.value = false;
      }
    };
    
    const fetchTripDetail = async () => {
      try {
        const tripId = route.params.id;
        if (!tripId) {
          return;
        }
        
        // 这里应该调用云开发的数据库查询
        // 示例代码，实际实现需要根据云开发SDK调整
        const app = cloudbase.init({
          env: 'your-env-id' // 环境ID需要替换
        });
        const db = app.database();
        
        // 获取旅行计划详情
        const { data } = await db.collection('trips').doc(tripId).get();
        if (!data || data.length === 0) {
          trip.value = null;
          return;
        }
        
        trip.value = data[0];
        
        // 获取作者信息
        if (trip.value.userId) {
          const { data: userData } = await db.collection('users').where({
            _openid: trip.value.userId
          }).get();
          
          if (userData && userData.length > 0) {
            author.value = userData[0];
          }
        }
        
        // 获取相关日记
        const { data: diaryData } = await db.collection('diaries')
          .where({
            tripId: tripId
          })
          .orderBy('date', 'desc')
          .get();
        
        diaries.value = diaryData || [];
        
        // 如果有目的地信息，初始化地图
        if (trip.value.destinations && trip.value.destinations.length > 0) {
          // 这里可以添加地图初始化代码
          // 例如使用高德地图、百度地图或谷歌地图API
        }
      } catch (error) {
        console.error('获取旅行计划详情失败:', error);
        trip.value = null;
      }
    };
    
    const editTrip = () => {
      router.push(`/edit-trip/${trip.value._id}`);
    };
    
    const confirmDeleteTrip = () => {
      const modal = document.getElementById('delete_confirm_modal');
      modal.showModal();
    };
    
    const closeDeleteModal = () => {
      const modal = document.getElementById('delete_confirm_modal');
      modal.close();
    };
    
    const deleteTrip = async () => {
      try {
        isDeleting.value = true;
        
        // 这里应该调用云开发的数据库操作
        // 示例代码，实际实现需要根据云开发SDK调整
        const app = cloudbase.init({
          env: 'your-env-id' // 环境ID需要替换
        });
        const db = app.database();
        
        // 删除旅行计划
        await db.collection('trips').doc(trip.value._id).remove();
        
        // 返回旅行计划列表页
        router.push('/trips');
      } catch (error) {
        console.error('删除旅行计划失败:', error);
        alert('删除失败，请重试');
      } finally {
        isDeleting.value = false;
      }
    };
    
    const createDiary = () => {
      router.push(`/create-diary?tripId=${trip.value._id}`);
    };
    
    const viewDiaryDetail = (diaryId) => {
      router.push(`/diary/${diaryId}`);
    };
    
    const goBack = () => {
      router.back();
    };
    
    const formatDate = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
    };
    
    return {
      isLoading,
      isDeleting,
      isLoggedIn,
      currentUserId,
      trip,
      author,
      diaries,
      isOwner,
      editTrip,
      confirmDeleteTrip,
      closeDeleteModal,
      deleteTrip,
      createDiary,
      viewDiaryDetail,
      goBack,
      formatDate
    };
  }
};
</script>

<style scoped>
.prose {
  max-width: 100%;
}

.prose p {
  margin-bottom: 1rem;
}

#map {
  width: 100%;
  height: 300px;
  border-radius: 0.5rem;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
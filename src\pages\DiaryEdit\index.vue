<template>
  <div class="min-h-screen bg-gray-50 p-4">
    <h1 class="text-2xl font-bold mb-4">编辑日记</h1>
    <input v-model="title" placeholder="标题" class="w-full p-2 mb-4 border" />
    <textarea v-model="content" placeholder="内容" class="w-full p-2 mb-4 border h-32"></textarea>
    <input type="file" @change="uploadMedia" multiple class="mb-4" />
    <button @click="saveDiary" class="bg-blue-500 text-white p-2">保存</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { db, app } from '@/utils/cloudbase'

const title = ref('')
const content = ref('')
const media = ref([])

const uploadMedia = async (e) => {
  const files = e.target.files
  for (let file of files) {
    const res = await app.uploadFile({
      cloudPath: `media/${file.name}`,
      filePath: file
    })
    media.value.push(res.fileID)
  }
}

const saveDiary = async () => {
  await db.collection('diaries').add({
    title: title.value,
    content: content.value,
    media: media.value,
    createdAt: new Date()
  })
  alert('日记保存成功')
}
</script>
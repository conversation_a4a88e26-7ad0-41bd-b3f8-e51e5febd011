<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="flex justify-center items-center h-64">
      <span class="loading loading-spinner loading-lg"></span>
    </div>
    
    <!-- 日记不存在 -->
    <div v-else-if="!diary" class="text-center py-16">
      <h2 class="text-2xl font-bold mb-4">未找到日记</h2>
      <p class="mb-8">该日记可能已被删除或您没有权限查看</p>
      <button class="btn btn-primary" @click="goBack">返回</button>
    </div>
    
    <!-- 日记详情 -->
    <div v-else>
      <!-- 返回按钮 -->
      <div class="mb-6">
        <button class="btn btn-ghost" @click="goBack">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          返回
        </button>
      </div>
      
      <!-- 日记标题和操作按钮 -->
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold">{{ diary.title }}</h1>
        
        <div v-if="isOwner" class="flex gap-2">
          <button class="btn btn-outline" @click="editDiary">编辑</button>
          <button class="btn btn-error" @click="confirmDeleteDiary">删除</button>
        </div>
      </div>
      
      <!-- 日记元信息 -->
      <div class="flex flex-wrap gap-4 mb-6 text-sm text-gray-500">
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          {{ formatDate(diary.date) }}
        </div>
        
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          {{ author.name || '匿名用户' }}
        </div>
        
        <div v-if="diary.location" class="flex items-center cursor-pointer" @click="openInMaps">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          {{ diary.location }}
        </div>
        
        <div v-if="relatedTrip" class="flex items-center cursor-pointer" @click="viewTripDetail(relatedTrip._id)">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
          </svg>
          {{ relatedTrip.title }}
        </div>
      </div>
      
      <!-- 标签 -->
      <div v-if="diary.tags && diary.tags.length > 0" class="mb-6">
        <div class="flex flex-wrap gap-2">
          <span v-for="tag in diary.tags" :key="tag" class="badge badge-outline">{{ tag }}</span>
        </div>
      </div>
      
      <!-- 封面图 -->
      <div v-if="diary.coverImage" class="mb-6">
        <img :src="diary.coverImage" alt="日记封面" class="w-full h-64 object-cover rounded-lg shadow-md">
      </div>
      
      <!-- 日记内容 -->
      <div class="prose max-w-none mb-8">
        <p v-if="diary.content" v-html="diary.content.replace(/\n/g, '<br>')"></p>
      </div>
      
      <!-- 照片集 -->
      <div v-if="diary.photos && diary.photos.length > 0" class="mb-8">
        <h3 class="text-xl font-bold mb-4">照片集</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div v-for="(photo, index) in diary.photos" :key="photo.id" class="aspect-square cursor-pointer" @click="openPhotoModal(index)">
            <img :src="photo.url" alt="日记照片" class="w-full h-full object-cover rounded-lg">
          </div>
        </div>
      </div>
      
      <!-- 地图 -->
      <div v-if="diary.location" class="mb-8">
        <h3 class="text-xl font-bold mb-4">位置</h3>
        <div id="map" class="h-64 rounded-lg shadow-md"></div>
      </div>
      
      <!-- 相关日记 -->
      <div v-if="relatedDiaries.length > 0" class="mb-8">
        <h3 class="text-xl font-bold mb-4">相关日记</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div v-for="relatedDiary in relatedDiaries" :key="relatedDiary._id" class="card bg-base-100 shadow-md cursor-pointer" @click="viewDiaryDetail(relatedDiary._id)">
            <figure v-if="relatedDiary.coverImage">
              <img :src="relatedDiary.coverImage" alt="日记封面" class="w-full h-32 object-cover">
            </figure>
            <div class="card-body p-4">
              <h4 class="card-title text-base">{{ relatedDiary.title }}</h4>
              <p class="text-sm text-gray-500">{{ formatDate(relatedDiary.date) }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 评论区 -->
      <div class="mb-8">
        <h3 class="text-xl font-bold mb-4">评论 ({{ comments.length }})</h3>
        
        <!-- 评论输入框 -->
        <div v-if="isLoggedIn" class="mb-6">
          <div class="flex gap-4">
            <div class="flex-shrink-0">
              <div class="avatar">
                <div class="w-10 h-10 rounded-full">
                  <img src="https://daisyui.com/images/stock/photo-1534528741775-53994a69daeb.jpg" alt="用户头像">
                </div>
              </div>
            </div>
            <div class="flex-grow">
              <textarea v-model="newComment" class="textarea textarea-bordered w-full" placeholder="写下你的评论..."></textarea>
              <div class="flex justify-end mt-2">
                <button class="btn btn-primary btn-sm" @click="submitComment" :disabled="isSubmitting || !newComment.trim()">
                  <span v-if="isSubmitting" class="loading loading-spinner loading-xs mr-1"></span>
                  发布评论
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 登录提示 -->
        <div v-else class="alert mb-6">
          <div>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-info flex-shrink-0 w-6 h-6">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>登录后才能发表评论</span>
          </div>
          <div class="flex-none">
            <button class="btn btn-sm btn-primary" @click="login">登录</button>
          </div>
        </div>
        
        <!-- 评论列表 -->
        <div v-if="comments.length > 0">
          <div v-for="comment in comments" :key="comment._id" class="flex gap-4 mb-6">
            <div class="flex-shrink-0">
              <div class="avatar">
                <div class="w-10 h-10 rounded-full">
                  <img :src="comment.userAvatar || 'https://daisyui.com/images/stock/photo-1534528741775-53994a69daeb.jpg'" alt="用户头像">
                </div>
              </div>
            </div>
            <div class="flex-grow">
              <div class="flex justify-between items-start">
                <div>
                  <p class="font-medium">{{ comment.userName || '用户' }}</p>
                  <p class="text-xs text-gray-500">{{ formatDateTime(comment.createdAt) }}</p>
                </div>
                <button v-if="currentUserId === comment.userId" class="btn btn-ghost btn-xs" @click="deleteComment(comment._id)">删除</button>
              </div>
              <p class="mt-2">{{ comment.content }}</p>
            </div>
          </div>
        </div>
        
        <!-- 无评论提示 -->
        <div v-else class="text-center py-8 text-gray-500">
          暂无评论，快来发表第一条评论吧！
        </div>
      </div>
    </div>
    
    <!-- 照片查看器对话框 -->
    <dialog id="photo_modal" class="modal">
      <div class="modal-box max-w-4xl h-auto">
        <div class="relative">
          <img v-if="diary && diary.photos && selectedPhotoIndex !== null" :src="diary.photos[selectedPhotoIndex].url" alt="照片大图" class="w-full h-auto">
          
          <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
            <button class="btn btn-circle" @click="prevPhoto" v-if="selectedPhotoIndex > 0">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          </div>
          
          <div class="absolute top-1/2 right-4 transform -translate-y-1/2">
            <button class="btn btn-circle" @click="nextPhoto" v-if="diary && diary.photos && selectedPhotoIndex < diary.photos.length - 1">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
        
        <div class="modal-action">
          <button class="btn" @click="closePhotoModal">关闭</button>
        </div>
      </div>
    </dialog>
    
    <!-- 删除确认对话框 -->
    <dialog id="delete_confirm_modal" class="modal">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">确认删除</h3>
        <p>您确定要删除这篇日记吗？此操作无法撤销。</p>
        <div class="modal-action">
          <button class="btn" @click="closeDeleteModal">取消</button>
          <button class="btn btn-error" @click="deleteDiary" :disabled="isDeleting">
            <span v-if="isDeleting" class="loading loading-spinner loading-xs mr-1"></span>
            删除
          </button>
        </div>
      </div>
    </dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

export default {
  name: 'DiaryDetailPage',
  setup() {
    const route = useRoute();
    const router = useRouter();
    
    // 状态变量
    const isLoading = ref(true);
    const isDeleting = ref(false);
    const isSubmitting = ref(false);
    const isLoggedIn = ref(false);
    const currentUserId = ref(null);
    const diary = ref(null);
    const author = ref({});
    const relatedTrip = ref(null);
    const relatedDiaries = ref([]);
    const comments = ref([]);
    const newComment = ref('');
    const selectedPhotoIndex = ref(null);
    
    // 计算属性
    const isOwner = computed(() => {
      return diary.value && currentUserId.value && diary.value._openid === currentUserId.value;
    });
    
    // 生命周期钩子
    onMounted(async () => {
      await checkLoginStatus();
      await fetchDiaryDetail();
      isLoading.value = false;
    });
    
    // 方法
    const checkLoginStatus = async () => {
      try {
        const loginState = await auth.checkLoginState();
        isLoggedIn.value = loginState;
        if (isLoggedIn.value) {
          const user = auth.getCurrentUser();
          currentUserId.value = user.uid;
        }
      } catch (error) {
        console.error('检查登录状态失败:', error);
        isLoggedIn.value = false;
      }
    };
    
    const login = async () => {
      try {
        // 这里应该调用云开发的登录页面
        // 示例代码，实际实现需要根据云开发SDK调整
        const app = cloudbase.init({
          env: 'your-env-id' // 环境ID需要替换
        });
        const auth = app.auth();
        await auth.toDefaultLoginPage();
      } catch (error) {
        console.error('登录失败:', error);
      }
    };
    
    const fetchDiaryDetail = async () => {
      try {
        const diaryId = route.params.id;
        if (!diaryId) {
          return;
        }
        
        // 这里应该调用云开发的数据库查询
        // 示例代码，实际实现需要根据云开发SDK调整
        const app = cloudbase.init({
          env: 'your-env-id' // 环境ID需要替换
        });
        const db = app.database();
        
        // 获取日记详情
        const { data } = await db.collection('diaries').doc(diaryId).get();
        if (!data || data.length === 0) {
          diary.value = null;
          return;
        }
        
        diary.value = data[0];
        
        // 获取作者信息
        if (diary.value._openid) {
          const { data: userData } = await db.collection('users').where({
            _openid: diary.value._openid
          }).get();
          
          if (userData && userData.length > 0) {
            author.value = userData[0];
          }
        }
        
        // 获取关联旅行计划
        if (diary.value.tripId) {
          const { data: tripData } = await db.collection('trips').doc(diary.value.tripId).get();
          if (tripData && tripData.length > 0) {
            relatedTrip.value = tripData[0];
          }
        }
        
        // 获取相关日记（同一旅行计划或同一地点的其他日记）
        const relatedQuery = db.collection('diaries').where({
          _id: db.command.neq(diaryId)
        });
        
        if (diary.value.tripId) {
          relatedQuery.where({
            tripId: diary.value.tripId
          });
        } else if (diary.value.location) {
          relatedQuery.where({
            location: diary.value.location
          });
        }
        
        const { data: relatedData } = await relatedQuery.limit(5).get();
        relatedDiaries.value = relatedData || [];
        
        // 获取评论
        await fetchComments();
        
        // 如果有位置信息，初始化地图
        if (diary.value.location) {
          // 这里可以添加地图初始化代码
          // 例如使用高德地图、百度地图或谷歌地图API
        }
      } catch (error) {
        console.error('获取日记详情失败:', error);
        diary.value = null;
      }
    };
    
    const fetchComments = async () => {
      try {
        const diaryId = route.params.id;
        if (!diaryId) {
          return;
        }
        
        // 这里应该调用云开发的数据库查询
        // 示例代码，实际实现需要根据云开发SDK调整
        const app = cloudbase.init({
          env: 'your-env-id' // 环境ID需要替换
        });
        const db = app.database();
        
        // 获取评论
        const { data } = await db.collection('comments')
          .where({
            diaryId: diaryId
          })
          .orderBy('createdAt', 'desc')
          .get();
        
        comments.value = data || [];
        
        // 获取评论用户信息
        if (comments.value.length > 0) {
          const userIds = [...new Set(comments.value.map(c => c.userId))];
          
          const { data: userData } = await db.collection('users')
            .where({
              _openid: db.command.in(userIds)
            })
            .get();
          
          if (userData && userData.length > 0) {
            const userMap = {};
            userData.forEach(user => {
              userMap[user._openid] = user;
            });
            
            comments.value = comments.value.map(comment => {
              const user = userMap[comment.userId];
              if (user) {
                comment.userName = user.name || '用户';
                comment.userAvatar = user.avatar;
              }
              return comment;
            });
          }
        }
      } catch (error) {
        console.error('获取评论失败:', error);
      }
    };
    
    const submitComment = async () => {
      if (!newComment.value.trim() || !isLoggedIn.value) {
        return;
      }
      
      try {
        isSubmitting.value = true;
        
        // 这里应该调用云开发的数据库操作
        // 示例代码，实际实现需要根据云开发SDK调整
        const app = cloudbase.init({
          env: 'your-env-id' // 环境ID需要替换
        });
        const db = app.database();
        
        const commentData = {
          diaryId: route.params.id,
          content: newComment.value.trim(),
          userId: currentUserId.value,
          createdAt: new Date()
        };
        
        // 添加评论
        const { id } = await db.collection('comments').add(commentData);
        
        // 更新本地数据
        comments.value.unshift({
          _id: id,
          ...commentData,
          userName: author.value.name || '我',
          userAvatar: author.value.avatar
        });
        
        // 清空输入框
        newComment.value = '';
      } catch (error) {
        console.error('提交评论失败:', error);
        alert('评论发布失败，请重试');
      } finally {
        isSubmitting.value = false;
      }
    };
    
    const deleteComment = async (commentId) => {
      try {
        // 这里应该调用云开发的数据库操作
        // 示例代码，实际实现需要根据云开发SDK调整
        const app = cloudbase.init({
          env: 'your-env-id' // 环境ID需要替换
        });
        const db = app.database();
        
        // 删除评论
        await db.collection('comments').doc(commentId).remove();
        
        // 更新本地数据
        comments.value = comments.value.filter(c => c._id !== commentId);
      } catch (error) {
        console.error('删除评论失败:', error);
        alert('删除评论失败，请重试');
      }
    };
    
    const editDiary = () => {
      router.push(`/edit-diary/${diary.value._id}`);
    };
    
    const confirmDeleteDiary = () => {
      const modal = document.getElementById('delete_confirm_modal');
      modal.showModal();
    };
    
    const closeDeleteModal = () => {
      const modal = document.getElementById('delete_confirm_modal');
      modal.close();
    };
    
    const deleteDiary = async () => {
      try {
        isDeleting.value = true;
        
        // 这里应该调用云开发的数据库操作
        // 示例代码，实际实现需要根据云开发SDK调整
        const app = cloudbase.init({
          env: 'your-env-id' // 环境ID需要替换
        });
        const db = app.database();
        
        // 删除日记
        await db.collection('diaries').doc(diary.value._id).remove();
        
        // 删除相关评论
        await db.collection('comments')
          .where({
            diaryId: diary.value._id
          })
          .remove();
        
        // 返回日记列表页
        router.push('/diaries');
      } catch (error) {
        console.error('删除日记失败:', error);
        alert('删除失败，请重试');
      } finally {
        isDeleting.value = false;
      }
    };
    
    const openPhotoModal = (index) => {
      selectedPhotoIndex.value = index;
      const modal = document.getElementById('photo_modal');
      modal.showModal();
    };
    
    const closePhotoModal = () => {
      const modal = document.getElementById('photo_modal');
      modal.close();
      selectedPhotoIndex.value = null;
    };
    
    const prevPhoto = () => {
      if (selectedPhotoIndex.value > 0) {
        selectedPhotoIndex.value--;
      }
    };
    
    const nextPhoto = () => {
      if (diary.value && diary.value.photos && selectedPhotoIndex.value < diary.value.photos.length - 1) {
        selectedPhotoIndex.value++;
      }
    };
    
    const viewTripDetail = (tripId) => {
      router.push(`/trip/${tripId}`);
    };
    
    const viewDiaryDetail = (diaryId) => {
      router.push(`/diary/${diaryId}`);
    };
    
    const openInMaps = () => {
      if (diary.value && diary.value.location) {
        // 这里可以根据平台打开不同的地图应用
        // 例如在移动端打开原生地图应用，在桌面端打开地图网站
        window.open(`https://maps.google.com/maps?q=${encodeURIComponent(diary.value.location)}`, '_blank');
      }
    };
    
    const goBack = () => {
      router.back();
    };
    
    const formatDate = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
    };
    
    const formatDateTime = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    };
    
    return {
      isLoading,
      isDeleting,
      isSubmitting,
      isLoggedIn,
      currentUserId,
      diary,
      author,
      relatedTrip,
      relatedDiaries,
      comments,
      newComment,
      selectedPhotoIndex,
      isOwner,
      checkLoginStatus,
      login,
      fetchDiaryDetail,
      fetchComments,
      submitComment,
      deleteComment,
      editDiary,
      confirmDeleteDiary,
      closeDeleteModal,
      deleteDiary,
      openPhotoModal,
      closePhotoModal,
      prevPhoto,
      nextPhoto,
      viewTripDetail,
      viewDiaryDetail,
      openInMaps,
      goBack,
      formatDate,
      formatDateTime
    };
  }
};
</script>

<style scoped>
.prose {
  max-width: 100%;
}

.prose p {
  margin-bottom: 1rem;
}

#map {
  width: 100%;
  height: 300px;
  border-radius: 0.5rem;
}
</style>
<template>
  <div class="location-picker">
    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        选择位置
      </label>
      <div class="relative">
        <input
          v-model="searchQuery"
          @input="handleSearch"
          type="text"
          placeholder="搜索地点或点击地图选择"
          class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
        <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
      
      <!-- 搜索结果 -->
      <div v-if="searchResults.length > 0" class="mt-2 border border-gray-200 rounded-lg max-h-40 overflow-y-auto">
        <div
          v-for="result in searchResults"
          :key="result.place_id"
          @click="selectLocation(result)"
          class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
        >
          <div class="font-medium text-sm">{{ result.display_name }}</div>
        </div>
      </div>
    </div>
    
    <!-- 小地图 -->
    <div class="h-48 border border-gray-300 rounded-lg overflow-hidden">
      <div ref="miniMap" class="w-full h-full"></div>
    </div>
    
    <!-- 选中的位置信息 -->
    <div v-if="selectedLocation" class="mt-3 p-3 bg-blue-50 rounded-lg">
      <div class="text-sm font-medium text-blue-900">已选择位置:</div>
      <div class="text-sm text-blue-700 mt-1">{{ selectedLocation.name }}</div>
      <div class="text-xs text-blue-600 mt-1">
        坐标: {{ selectedLocation.lat.toFixed(6) }}, {{ selectedLocation.lng.toFixed(6) }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import L from 'leaflet'

export default {
  name: 'LocationPicker',
  props: {
    modelValue: {
      type: Object,
      default: null
    },
    initialCenter: {
      type: Array,
      default: () => [39.9042, 116.4074]
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const miniMap = ref(null)
    const searchQuery = ref('')
    const searchResults = ref([])
    const selectedLocation = ref(props.modelValue)
    
    let map = null
    let marker = null
    
    // 初始化小地图
    const initMiniMap = async () => {
      await nextTick()
      
      map = L.map(miniMap.value, {
        center: props.initialCenter,
        zoom: 13,
        zoomControl: true
      })
      
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map)
      
      // 地图点击事件
      map.on('click', (e) => {
        setLocation(e.latlng.lat, e.latlng.lng)
      })
      
      // 如果有初始值，显示标记
      if (selectedLocation.value) {
        setLocation(selectedLocation.value.lat, selectedLocation.value.lng, selectedLocation.value.name)
      }
    }
    
    // 设置位置
    const setLocation = (lat, lng, name = '') => {
      // 移除现有标记
      if (marker) {
        map.removeLayer(marker)
      }
      
      // 添加新标记
      marker = L.marker([lat, lng]).addTo(map)
      
      // 更新选中位置
      selectedLocation.value = {
        lat,
        lng,
        name: name || `位置 ${lat.toFixed(4)}, ${lng.toFixed(4)}`
      }
      
      // 发出更新事件
      emit('update:modelValue', selectedLocation.value)
      
      // 移动地图中心
      map.setView([lat, lng], map.getZoom())
    }
    
    // 搜索处理
    let searchTimeout = null
    const handleSearch = () => {
      clearTimeout(searchTimeout)
      searchTimeout = setTimeout(() => {
        if (searchQuery.value.length > 2) {
          searchLocation()
        } else {
          searchResults.value = []
        }
      }, 300)
    }
    
    // 搜索地点
    const searchLocation = async () => {
      try {
        const response = await fetch(
          `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchQuery.value)}&limit=5`
        )
        const data = await response.json()
        searchResults.value = data
      } catch (error) {
        console.error('搜索失败:', error)
      }
    }
    
    // 选择搜索结果
    const selectLocation = (result) => {
      const lat = parseFloat(result.lat)
      const lng = parseFloat(result.lon)
      
      setLocation(lat, lng, result.display_name)
      searchResults.value = []
      searchQuery.value = result.display_name
    }
    
    // 监听外部值变化
    watch(() => props.modelValue, (newValue) => {
      if (newValue && newValue !== selectedLocation.value) {
        selectedLocation.value = newValue
        if (map && newValue.lat && newValue.lng) {
          setLocation(newValue.lat, newValue.lng, newValue.name)
        }
      }
    })
    
    onMounted(() => {
      initMiniMap()
    })
    
    onUnmounted(() => {
      if (map) {
        map.remove()
      }
    })
    
    return {
      miniMap,
      searchQuery,
      searchResults,
      selectedLocation,
      handleSearch,
      selectLocation
    }
  }
}
</script>
# 部署指南：将项目部署到云开发静态托管

## 前提条件

1. 已安装Node.js和npm
2. 已有腾讯云账号并开通云开发服务
3. 已创建云开发环境，环境ID为：`w12-1g8r8jq7b8f4acf4`

## 部署步骤

### 1. 安装云开发CLI工具

```bash
npm install -g @cloudbase/cli
```

### 2. 登录云开发

```bash
tcb login
```

执行后会打开浏览器，使用微信或QQ扫码登录。

### 3. 构建项目

```bash
npm run build
```

### 4. 部署到云开发静态托管

```bash
tcb framework deploy
```

系统会根据项目根目录下的`cloudbaserc.json`配置文件进行部署。

### 5. 访问部署后的网站

部署成功后，CLI会输出网站访问地址，通常格式为：
`https://{环境ID}.service.tcloudbase.com/travel-diary/`

## 注意事项

1. 确保云开发环境已开通静态网站托管服务
2. 如果遇到权限问题，请确认登录账号有对应环境的操作权限
3. 首次部署可能需要等待几分钟才能访问

## 自动化部署（可选）

如果需要设置CI/CD自动部署，可以参考云开发官方文档配置GitHub Actions或其他CI工具。
import cloudbase from '@cloudbase/js-sdk';

// 初始化云开发SDK
const app = cloudbase.init({
  env: 'w12-1g8r8jq7b8f4acf4' // 云开发环境ID
});

// 打印初始化状态
console.log('CloudBase initialized:', app);

// 获取数据库实例
const auth = app.auth();
const db = app.database();

// 检查登录状态
const checkLogin = async () => {
  const loginState = await auth.getLoginState();
  if (!loginState || !loginState.isLoggedIn) {
    // 未登录，进行匿名登录
    await auth.signInAnonymously();
  }
  return auth.getLoginState();
};

/**
 * 获取日记列表
 * @returns {Promise<Array>} 日记数据数组
 */
const getDiaries = async () => {
  await checkLogin();
  try {
    const result = await db.collection('diaries').get();
    console.log('获取到的日记数据:', result.data);
    return result.data;
  } catch (error) {
    console.error('获取日记列表失败:', error);
    throw error;
  }
};

/**
 * 创建新日记
 * @param {Object} diaryData 日记数据
 * @returns {Promise} 创建结果
 */
const createDiary = async (diaryData) => {
  await checkLogin();
  try {
    const result = await db.collection('diaries').add(diaryData);
    console.log('日记创建成功:', result);
    return result;
  } catch (error) {
    console.error('创建日记失败:', error);
    throw error;
  }
};

/**
 * 调用云函数
 * @param {string} name 云函数名称
 * @param {Object} data 调用参数
 * @returns {Promise} 云函数调用结果
 */
const callFunction = async (name, data) => {
  await checkLogin();
  try {
    const result = await app.callFunction({
      name,
      data
    });
    console.log(`云函数${name}调用成功:`, result);
    return result;
  } catch (error) {
    console.error(`云函数${name}调用失败:`, error);
    throw error;
  }
};

// 添加上传文件方法
const uploadFile = async (cloudPath, filePath) => {
  return await app.uploadFile({
    cloudPath,
    filePath
  })
}

export { app, auth, db, checkLogin, getDiaries, createDiary, callFunction, uploadFile };
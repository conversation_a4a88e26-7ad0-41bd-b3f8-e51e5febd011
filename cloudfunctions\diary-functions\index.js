const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command
const $ = db.command.aggregate

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event
  const wxContext = cloud.getWXContext()
  const openId = wxContext.OPENID

  switch (action) {
    case 'createDiary':
      return await createDiary(data, openId)
    case 'updateDiary':
      return await updateDiary(data, openId)
    case 'deleteDiary':
      return await deleteDiary(data, openId)
    case 'getDiaryList':
      return await getDiaryList(data, openId)
    case 'getDiaryDetail':
      return await getDiaryDetail(data)
    default:
      return {
        code: 404,
        message: '未找到对应的操作'
      }
  }
}

/**
 * 创建日记
 */
async function createDiary(data, openId) {
  try {
    const { title, content, location, images, tripId } = data
    
    const diaryData = {
      title,
      content,
      location,
      images: images || [],
      tripId,
      likes: 0,
      comments: 0,
      _openid: openId,
      createdAt: db.serverDate(),
      updatedAt: db.serverDate()
    }

    const result = await db.collection('diaries').add({
      data: diaryData
    })

    // 如果关联了旅行计划，更新旅行计划的日记数量
    if (tripId) {
      await db.collection('trips').doc(tripId).update({
        data: {
          diaryCount: _.inc(1),
          updatedAt: db.serverDate()
        }
      })
    }

    return {
      code: 200,
      data: {
        _id: result._id,
        ...diaryData
      }
    }
  } catch (err) {
    console.error('创建日记失败:', err)
    return {
      code: 500,
      message: '创建日记失败'
    }
  }
}

/**
 * 更新日记
 */
async function updateDiary(data, openId) {
  try {
    const { diaryId, title, content, location, images } = data
    
    // 检查是否是日记的创建者
    const diary = await db.collection('diaries').doc(diaryId).get()
    if (diary.data._openid !== openId) {
      return {
        code: 403,
        message: '无权限修改此日记'
      }
    }

    const updateData = {
      updatedAt: db.serverDate()
    }

    if (title !== undefined) updateData.title = title
    if (content !== undefined) updateData.content = content
    if (location !== undefined) updateData.location = location
    if (images !== undefined) updateData.images = images

    await db.collection('diaries').doc(diaryId).update({
      data: updateData
    })

    return {
      code: 200,
      message: '更新日记成功'
    }
  } catch (err) {
    console.error('更新日记失败:', err)
    return {
      code: 500,
      message: '更新日记失败'
    }
  }
}

/**
 * 删除日记
 */
async function deleteDiary(data, openId) {
  try {
    const { diaryId } = data
    
    // 检查是否是日记的创建者
    const diary = await db.collection('diaries').doc(diaryId).get()
    if (diary.data._openid !== openId) {
      return {
        code: 403,
        message: '无权限删除此日记'
      }
    }

    // 如果关联了旅行计划，更新旅行计划的日记数量
    if (diary.data.tripId) {
      await db.collection('trips').doc(diary.data.tripId).update({
        data: {
          diaryCount: _.inc(-1),
          updatedAt: db.serverDate()
        }
      })
    }

    // 删除日记相关的评论
    await db.collection('comments').where({
      diaryId
    }).remove()

    // 删除日记
    await db.collection('diaries').doc(diaryId).remove()

    return {
      code: 200,
      message: '删除日记成功'
    }
  } catch (err) {
    console.error('删除日记失败:', err)
    return {
      code: 500,
      message: '删除日记失败'
    }
  }
}

/**
 * 获取日记列表
 */
async function getDiaryList(data, openId) {
  try {
    const { tripId, userId, page = 1, pageSize = 10 } = data
    const skip = (page - 1) * pageSize
    
    let query = db.collection('diaries')
    
    // 根据旅行计划ID筛选
    if (tripId) {
      query = query.where({
        tripId
      })
    }
    
    // 根据用户ID筛选
    if (userId) {
      query = query.where({
        _openid: userId
      })
    }
    
    // 获取总数
    const countResult = await query.count()
    
    // 获取分页数据
    const { data: diaries } = await query
      .orderBy('createdAt', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get()
    
    // 获取每个日记的作者信息
    const userIds = [...new Set(diaries.map(diary => diary._openid))]
    const { data: users } = await db.collection('users')
      .where({
        _openid: _.in(userIds)
      })
      .get()
    
    const userMap = {}
    users.forEach(user => {
      userMap[user._openid] = user
    })
    
    const result = diaries.map(diary => {
      const user = userMap[diary._openid] || {}
      return {
        ...diary,
        author: {
          _id: user._id,
          name: user.name,
          avatar: user.avatar
        }
      }
    })
    
    return {
      code: 200,
      data: {
        list: result,
        total: countResult.total,
        page,
        pageSize
      }
    }
  } catch (err) {
    console.error('获取日记列表失败:', err)
    return {
      code: 500,
      message: '获取日记列表失败'
    }
  }
}

/**
 * 获取日记详情
 */
async function getDiaryDetail(data) {
  try {
    const { diaryId } = data
    
    const { data: diary } = await db.collection('diaries').doc(diaryId).get()
    
    if (!diary) {
      return {
        code: 404,
        message: '日记不存在'
      }
    }
    
    // 获取作者信息
    const { data: users } = await db.collection('users')
      .where({
        _openid: diary._openid
      })
      .get()
    
    const author = users[0] || {}
    
    return {
      code: 200,
      data: {
        ...diary,
        author: {
          _id: author._id,
          name: author.name,
          avatar: author.avatar
        }
      }
    }
  } catch (err) {
    console.error('获取日记详情失败:', err)
    return {
      code: 500,
      message: '获取日记详情失败'
    }
  }
}
<template>
  <div class="min-h-screen bg-gray-50 p-4">
    <h1 class="text-2xl font-bold mb-4">旅行时间线</h1>
    <div v-for="diary in diaries" :key="diary._id" class="mb-4 p-4 border">
      <h2>{{ diary.title }}</h2>
      <p>{{ formatDate(diary.createdAt) }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { db } from '@/utils/cloudbase'

const diaries = ref([])

onMounted(async () => {
  const res = await db.collection('diaries').orderBy('createdAt', 'desc').get()
  diaries.value = res.data
})

const formatDate = (date) => new Date(date).toLocaleString()
</script> 
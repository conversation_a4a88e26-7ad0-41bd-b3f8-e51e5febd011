<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- 主要内容区域 -->
    <div class="pb-16"> <!-- 为底部导航留出空间 -->
      <router-view />
    </div>
    
    <!-- 底部导航 -->
    <BottomNav />
  </div>
</template>

<script>
import { onMounted } from 'vue'
import { checkLogin } from '@/utils/cloudbase'
import BottomNav from '@/components/BottomNav/BottomNav.vue'

export default {
  name: 'App',
  components: {
    BottomNav
  },
  setup() {
    onMounted(async () => {
      try {
        await checkLogin()
        console.log('应用初始化成功')
      } catch (error) {
        console.error('初始化登录失败:', error)
      }
    })
  }
}
</script>

<style>
/* 全局样式 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  @apply text-gray-800 bg-gray-50;
}

/* 路由过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
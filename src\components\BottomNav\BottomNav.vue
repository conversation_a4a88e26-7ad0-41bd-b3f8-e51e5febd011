<template>
  <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-40">
    <div class="flex justify-around items-center py-2">
      <router-link 
        v-for="item in navItems" 
        :key="item.name"
        :to="item.path"
        class="flex flex-col items-center py-2 px-3 rounded-lg transition-colors"
        :class="{
          'text-blue-500': $route.path === item.path,
          'text-gray-500 hover:text-gray-700': $route.path !== item.path
        }"
      >
        <span class="text-xl mb-1">{{ item.icon }}</span>
        <span class="text-xs font-medium">{{ item.label }}</span>
      </router-link>
    </div>
  </nav>
</template>

<script>
export default {
  name: 'BottomNav',
  setup() {
    const navItems = [
      {
        name: 'home',
        path: '/',
        label: '首页',
        icon: '🏠'
      },
      {
        name: 'map',
        path: '/map',
        label: '地图',
        icon: '🗺️'
      },
      {
        name: 'edit',
        path: '/diary/edit',
        label: '写日记',
        icon: '✏️'
      },
      {
        name: 'timeline',
        path: '/timeline',
        label: '时间线',
        icon: '⏰'
      },
      {
        name: 'stats',
        path: '/stats',
        label: '统计',
        icon: '📊'
      }
    ]
    
    return {
      navItems
    }
  }
}
</script>
# 旅行日记 Web 应用

这是一个基于 Vue 3 和腾讯云开发的旅行日记 Web 应用。

## 项目结构

```
src/
├── pages/                # 页面组件
│   ├── Auth/             # 登录认证页面
│   ├── Home/             # 首页
│   ├── Profile/          # 用户资料页
│   └── ...               # 其他页面
├── router/               # 路由配置
└── App.vue               # 根组件
```

## 项目功能

- 交互式地图: 在地图上标记旅行地点
- 日记编辑: 添加照片、视频和内容
- 时间线查看: 按时间浏览旅行历史
- 旅行统计: 查看访问地点数、日记数等
- 分享行程: 与好友分享

## 云开发资源

- 数据库集合: trips, diaries, locations (存储位置、日记、多媒体链接)
- 云存储: 用于照片和视频
- 云函数: (如果需要) 处理统计和分享逻辑

## 云开发配置

- 环境ID: `
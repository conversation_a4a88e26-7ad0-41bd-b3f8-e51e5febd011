<template>
  <div class="auth-container">
    <div v-if="!isLoggedIn" class="auth-form">
      <h2>欢迎使用旅行日记</h2>
      
      <div class="form-group">
        <label>邮箱</label>
        <input v-model="email" type="email" placeholder="请输入邮箱">
      </div>
      
      <div class="form-group">
        <label>密码</label>
        <input v-model="password" type="password" placeholder="请输入密码">
      </div>
      
      <button @click="handleLogin" class="auth-btn primary">登录</button>
      <button @click="handleAnonymousLogin" class="auth-btn">游客体验</button>
    </div>
    
    <div v-else class="user-info">
      <h3>已登录</h3>
      <p>用户ID: {{ currentUser.uid }}</p>
      <button @click="handleLogout" class="auth-btn">退出登录</button>
    </div>
  </div>
</template>

<script>
import { authService as auth } from '@/utils/auth'

export default {
  name: 'AuthPage',
  data() {
    return {
      email: '',
      password: '',
      isLoggedIn: false,
      currentUser: null
    }
  },
  async created() {
    this.isLoggedIn = await auth.checkLoginState()
    if (this.isLoggedIn) {
      this.currentUser = auth.getCurrentUser()
    }
  },
  methods: {
    async handleLogin() {
      try {
        await auth.emailLogin(this.email, this.password)
        this.isLoggedIn = true
        this.currentUser = auth.getCurrentUser()
        this.$router.push('/')
      } catch (error) {
        alert('登录失败: ' + error.message)
      }
    },
    async handleAnonymousLogin() {
      try {
        await auth.anonymousLogin()
        this.isLoggedIn = true
        this.currentUser = auth.getCurrentUser()
        this.$router.push('/')
      } catch (error) {
        alert('匿名登录失败: ' + error.message)
      }
    },
    async handleLogout() {
      await auth.logout()
      this.isLoggedIn = false
      this.currentUser = null
    }
  }
}
</script>

<style scoped>
.auth-container {
  max-width: 400px;
  margin: 2rem auto;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #333;
}

.form-group input {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.auth-btn {
  padding: 0.75rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.auth-btn.primary {
  background: #42b983;
  color: white;
}

.auth-btn.primary:hover {
  background: #3aa876;
}

.auth-btn {
  background: #f0f0f0;
  color: #333;
}

.auth-btn:hover {
  background: #e0e0e0;
}

.user-info {
  text-align: center;
  padding: 1rem;
}

.user-info h3 {
  margin-bottom: 1rem;
  color: #42b983;
}
</style>
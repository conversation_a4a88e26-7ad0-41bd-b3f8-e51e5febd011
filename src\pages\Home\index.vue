<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <NavBar title="旅行日记" />

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 py-8">
      <!-- 搜索栏 -->
      <div class="mb-8">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索旅行日记..."
          class="w-full max-w-md px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
        <p class="mt-4 text-gray-600">加载中...</p>
      </div>

      <!-- 错误提示 -->
      <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
        <p>{{ error }}</p>
      </div>

      <!-- 日记列表 -->
      <div v-if="filteredDiaries.length > 0" class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <DiaryCard 
          v-for="diary in filteredDiaries" 
          :key="diary._id"
          :title="diary.title"
          :date="formatDate(diary.createdAt)"
          :summary="diary.content.substring(0, 100)"
          :image="diary.coverImage"
          @click="viewDiary(diary._id)"
        />
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && diaries.length === 0" class="text-center py-12">
        <div class="mx-auto h-24 w-24 text-gray-400">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 class="mt-2 text-lg font-medium text-gray-900">暂无日记</h3>
        <p class="mt-1 text-gray-500">点击下方按钮创建你的第一篇旅行日记</p>
        <div class="mt-6">
          <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            新建日记
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getDiaries } from '@/utils/cloudbase'
import DiaryCard from '@/components/DiaryCard/DiaryCard.vue'
import NavBar from '@/components/NavBar/NavBar.vue'

export default {
  components: {
    DiaryCard,
    NavBar
  },
  setup() {
    const diaries = ref([])
    const loading = ref(true)
    const error = ref(null)
    const searchQuery = ref('')
    
    // 根据搜索关键词过滤日记
    const filteredDiaries = computed(() => {
      if (!searchQuery.value) return diaries.value
      
      return diaries.value.filter(diary => 
        diary.title.toLowerCase().includes(searchQuery.value.toLowerCase()) || 
        diary.content.toLowerCase().includes(searchQuery.value.toLowerCase())
      )
    })

    const fetchDiaries = async () => {
      try {
        loading.value = true
        const data = await getDiaries()
        diaries.value = data.map(diary => ({
          ...diary,
          createdAt: diary.createTime,
          coverImage: diary.imageUrl
        }))
      } catch (err) {
        error.value = '加载日记失败: ' + err.message
      } finally {
        loading.value = false
      }
    }

    const formatDate = (timestamp) => {
      return new Date(timestamp).toLocaleDateString()
    }

    const viewDiary = (id) => {
      // 路由跳转到日记详情页
      console.log('查看日记:', id)
    }

    onMounted(() => {
      fetchDiaries()
    })

    return {
      diaries,
      loading,
      error,
      searchQuery,
      filteredDiaries,
      formatDate,
      viewDiary
    }
  }
}
</script>
<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="flex justify-center items-center h-64">
      <span class="loading loading-spinner loading-lg"></span>
    </div>
    
    <!-- 未登录状态 -->
    <div v-else-if="!isLoggedIn" class="text-center py-16">
      <h2 class="text-2xl font-bold mb-4">请登录查看个人资料</h2>
      <p class="mb-8">登录后可以查看和管理您的个人资料、旅行计划和日记</p>
      <button class="btn btn-primary" @click="login">登录</button>
    </div>
    
    <!-- 已登录状态 -->
    <div v-else>
      <!-- 个人资料卡片 -->
      <div class="card bg-base-100 shadow-xl mb-8">
        <div class="card-body">
          <div class="flex flex-col md:flex-row items-center md:items-start gap-6">
            <!-- 头像 -->
            <div class="avatar">
              <div class="w-24 h-24 rounded-full">
                <img :src="userProfile.avatar || 'https://daisyui.com/images/stock/photo-1534528741775-53994a69daeb.jpg'" alt="用户头像">
              </div>
            </div>
            
            <!-- 用户信息 -->
            <div class="flex-grow text-center md:text-left">
              <h2 class="text-2xl font-bold mb-2">{{ userProfile.name || '未设置昵称' }}</h2>
              <p v-if="userProfile.bio" class="mb-4 text-gray-600">{{ userProfile.bio }}</p>
              <p v-else class="mb-4 text-gray-400 italic">暂无个人简介</p>
              
              <div class="flex flex-wrap gap-4 justify-center md:justify-start mb-4">
                <div class="stat-value text-lg">{{ trips.length }} <span class="text-sm text-gray-500">旅行计划</span></div>
                <div class="stat-value text-lg">{{ diaries.length }} <span class="text-sm text-gray-500">旅行日记</span></div>
              </div>
              
              <button class="btn btn-outline btn-sm" @click="openEditProfileModal">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
                编辑资料
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 标签页导航 -->
      <div class="tabs tabs-boxed mb-6">
        <a 
          class="tab" 
          :class="{ 'tab-active': activeTab === 'trips' }"
          @click="activeTab = 'trips'"
        >
          我的旅行计划
        </a>
        <a 
          class="tab" 
          :class="{ 'tab-active': activeTab === 'diaries' }"
          @click="activeTab = 'diaries'"
        >
          我的旅行日记
        </a>
      </div>
      
      <!-- 旅行计划列表 -->
      <div v-if="activeTab === 'trips'">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-bold">我的旅行计划 ({{ trips.length }})</h3>
          <button class="btn btn-primary btn-sm" @click="createTrip">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            新建旅行计划
          </button>
        </div>
        
        <!-- 旅行计划列表 -->
        <div v-if="trips.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="trip in trips" :key="trip._id" class="card bg-base-100 shadow-md cursor-pointer hover:shadow-lg transition-shadow" @click="viewTripDetail(trip._id)">
            <figure v-if="trip.coverImage">
              <img :src="trip.coverImage" alt="旅行计划封面" class="w-full h-48 object-cover">
            </figure>
            <div class="card-body">
              <h4 class="card-title">{{ trip.title }}</h4>
              <p class="text-sm text-gray-500">{{ formatDate(trip.startDate) }} - {{ formatDate(trip.endDate) }}</p>
              <div v-if="trip.destinations && trip.destinations.length > 0" class="flex flex-wrap gap-1 mt-2">
                <span v-for="(destination, index) in trip.destinations" :key="index" class="badge badge-sm">{{ destination }}</span>
              </div>
              <div class="card-actions justify-end mt-2">
                <div class="badge badge-outline" :class="trip.isPublic ? 'badge-success' : 'badge-secondary'">
                  {{ trip.isPublic ? '公开' : '私密' }}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 无旅行计划提示 -->
        <div v-else class="text-center py-16 bg-base-200 rounded-lg">
          <h4 class="text-xl font-bold mb-2">还没有旅行计划</h4>
          <p class="mb-6 text-gray-500">开始规划你的旅行吧</p>
          <button class="btn btn-primary" @click="createTrip">创建第一个旅行计划</button>
        </div>
      </div>
      
      <!-- 日记列表 -->
      <div v-if="activeTab === 'diaries'">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-bold">我的旅行日记 ({{ diaries.length }})</h3>
          <button class="btn btn-primary btn-sm" @click="createDiary">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            写新日记
          </button>
        </div>
        
        <!-- 日记列表 -->
        <div v-if="diaries.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="diary in diaries" :key="diary._id" class="card bg-base-100 shadow-md cursor-pointer hover:shadow-lg transition-shadow" @click="viewDiaryDetail(diary._id)">
            <figure v-if="diary.coverImage">
              <img :src="diary.coverImage" alt="日记封面" class="w-full h-48 object-cover">
            </figure>
            <div class="card-body">
              <h4 class="card-title">{{ diary.title }}</h4>
              <p class="text-sm text-gray-500">{{ formatDate(diary.date) }}</p>
              <p v-if="diary.location" class="text-sm flex items-center text-gray-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                {{ diary.location }}
              </p>
              <p v-if="diary.content" class="line-clamp-2 text-gray-600">{{ diary.content }}</p>
              <div v-if="diary.tags && diary.tags.length > 0" class="flex flex-wrap gap-1 mt-2">
                <span v-for="tag in diary.tags" :key="tag" class="badge badge-sm">{{ tag }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 无日记提示 -->
        <div v-else class="text-center py-16 bg-base-200 rounded-lg">
          <h4 class="text-xl font-bold mb-2">还没有旅行日记</h4>
          <p class="mb-6 text-gray-500">记录下你的旅行回忆吧</p>
          <button class="btn btn-primary" @click="createDiary">写第一篇日记</button>
        </div>
      </div>
    </div>
    
    <!-- 编辑个人资料对话框 -->
    <dialog id="edit_profile_modal" class="modal">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">编辑个人资料</h3>
        
        <form @submit.prevent="updateProfile">
          <!-- 头像上传 -->
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">头像</span>
            </label>
            <div class="flex items-center gap-4">
              <div class="avatar">
                <div class="w-16 h-16 rounded-full">
                  <img :src="avatarPreview || userProfile.avatar || 'https://daisyui.com/images/stock/photo-1534528741775-53994a69daeb.jpg'" alt="头像预览">
                </div>
              </div>
              <input type="file" class="file-input file-input-bordered w-full max-w-xs" accept="image/*" @change="handleAvatarChange" />
            </div>
          </div>
          
          <!-- 昵称 -->
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">昵称</span>
            </label>
            <input type="text" v-model="profileForm.name" class="input input-bordered" placeholder="请输入昵称" />
          </div>
          
          <!-- 个人简介 -->
          <div class="form-control mb-6">
            <label class="label">
              <span class="label-text">个人简介</span>
            </label>
            <textarea v-model="profileForm.bio" class="textarea textarea-bordered h-24" placeholder="介绍一下自己吧"></textarea>
          </div>
          
          <div class="modal-action">
            <button type="button" class="btn" @click="closeEditProfileModal">取消</button>
            <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
              <span v-if="isSubmitting" class="loading loading-spinner loading-xs mr-1"></span>
              保存
            </button>
          </div>
        </form>
      </div>
    </dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';

export default {
  name: 'ProfilePage',
  setup() {
    const router = useRouter();
    
    // 状态变量
    const isLoading = ref(true);
    const isLoggedIn = ref(false);
    const isSubmitting = ref(false);
    const userProfile = ref({});
    const trips = ref([]);
    const diaries = ref([]);
    const activeTab = ref('trips');
    const avatarPreview = ref(null);
    
    // 表单数据
    const profileForm = reactive({
      name: '',
      bio: '',
      avatar: null
    });
    
    // 生命周期钩子
    onMounted(async () => {
      await checkLoginStatus();
      if (isLoggedIn.value) {
        await Promise.all([
          fetchUserProfile(),
          fetchUserTrips(),
          fetchUserDiaries()
        ]);
      }
      isLoading.value = false;
    });
    
    // 方法
    const checkLoginStatus = async () => {
      try {
        // 调用云开发的登录状态检查
        const app = cloudbase.init({
          env: 'w12-1g8r8jq7b8f4acf4' // 云开发环境ID
        });
        const auth = app.auth();
        const loginState = await auth.getLoginState();
        
        isLoggedIn.value = loginState && loginState.isLoggedIn;
      } catch (error) {
        console.error('检查登录状态失败:', error);
        isLoggedIn.value = false;
      }
    };
    
    const login = async () => {
      try {
        // 调用云开发的登录页面
        const app = cloudbase.init({
          env: 'w12-1g8r8jq7b8f4acf4' // 云开发环境ID
        });
        const auth = app.auth();
        await auth.toDefaultLoginPage();
      } catch (error) {
        console.error('登录失败:', error);
      }
    };
    
    const fetchUserProfile = async () => {
      try {
        // 调用云开发的数据库查询
        const app = cloudbase.init({
          env: 'w12-1g8r8jq7b8f4acf4' // 云开发环境ID
        });
        const auth = app.auth();
        const db = app.database();
        
        const loginState = await auth.getLoginState();
        if (!loginState) {
          return;
        }
        
        const userId = loginState.user.uid;
        
        // 获取用户资料
        const { data } = await db.collection('users').where({
          _openid: userId
        }).get();
        
        if (data && data.length > 0) {
          userProfile.value = data[0];
          
          // 初始化表单数据
          profileForm.name = userProfile.value.name || '';
          profileForm.bio = userProfile.value.bio || '';
        } else {
          // 如果用户资料不存在，创建一个新的
          const newUser = {
            name: '新用户',
            bio: '',
            createdAt: new Date()
          };
          
          const { id } = await db.collection('users').add(newUser);
          userProfile.value = { _id: id, ...newUser };
          
          // 初始化表单数据
          profileForm.name = userProfile.value.name;
          profileForm.bio = userProfile.value.bio;
        }
      } catch (error) {
        console.error('获取用户资料失败:', error);
      }
    };
    
    const fetchUserTrips = async () => {
      try {
        // 调用云开发的数据库查询
        const app = cloudbase.init({
          env: 'w12-1g8r8jq7b8f4acf4' // 云开发环境ID
        });
        const auth = app.auth();
        const db = app.database();
        
        const loginState = await auth.getLoginState();
        if (!loginState) {
          return;
        }
        
        const userId = loginState.user.uid;
        
        // 获取用户的旅行计划
        const { data } = await db.collection('trips')
          .where({
            userId: userId
          })
          .orderBy('startDate', 'desc')
          .get();
        
        trips.value = data || [];
      } catch (error) {
        console.error('获取旅行计划失败:', error);
      }
    };
    
    const fetchUserDiaries = async () => {
      try {
        // 调用云开发的数据库查询
        const app = cloudbase.init({
          env: 'w12-1g8r8jq7b8f4acf4' // 云开发环境ID
        });
        const auth = app.auth();
        const db = app.database();
        
        const loginState = await auth.getLoginState();
        if (!loginState) {
          return;
        }
        
        const userId = loginState.user.uid;
        
        // 获取用户的日记
        const { data } = await db.collection('diaries')
          .where({
            _openid: userId
          })
          .orderBy('date', 'desc')
          .get();
        
        diaries.value = data || [];
      } catch (error) {
        console.error('获取日记失败:', error);
      }
    };
    
    const openEditProfileModal = () => {
      // 重置表单数据
      profileForm.name = userProfile.value.name || '';
      profileForm.bio = userProfile.value.bio || '';
      profileForm.avatar = null;
      avatarPreview.value = null;
      
      const modal = document.getElementById('edit_profile_modal');
      modal.showModal();
    };
    
    const closeEditProfileModal = () => {
      const modal = document.getElementById('edit_profile_modal');
      modal.close();
    };
    
    const handleAvatarChange = (event) => {
      const file = event.target.files[0];
      if (!file) return;
      
      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        alert('请选择图片文件');
        return;
      }
      
      // 验证文件大小（限制为2MB）
      if (file.size > 2 * 1024 * 1024) {
        alert('图片大小不能超过2MB');
        return;
      }
      
      // 保存文件对象
      profileForm.avatar = file;
      
      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => {
        avatarPreview.value = e.target.result;
      };
      reader.readAsDataURL(file);
    };
    
    const updateProfile = async () => {
      try {
        isSubmitting.value = true;
        
        // 调用云开发的数据库操作和存储操作
        const app = cloudbase.init({
          env: 'your-env-id' // 环境ID需要替换
        });
        const auth = app.auth();
        const db = app.database();
        const storage = app.storage();
        
        const loginState = await auth.getLoginState();
        if (!loginState) {
          throw new Error('用户未登录');
        }
        
        let avatarUrl = userProfile.value.avatar;
        
        // 如果有新头像，上传到云存储
        if (profileForm.avatar) {
          const result = await storage.uploadFile({
            cloudPath: `avatars/${loginState.user.uid}_${Date.now()}.${profileForm.avatar.name.split('.').pop()}`,
            filePath: profileForm.avatar
          });
          
          // 获取文件访问链接
          const { fileList } = await storage.getTempFileURL({
            fileList: [result.fileID]
          });
          
          avatarUrl = fileList[0].tempFileURL;
        }
        
        // 更新用户资料
        await db.collection('users').doc(userProfile.value._id).update({
          name: profileForm.name,
          bio: profileForm.bio,
          avatar: avatarUrl,
          updatedAt: new Date()
        });
        
        // 更新本地数据
        userProfile.value = {
          ...userProfile.value,
          name: profileForm.name,
          bio: profileForm.bio,
          avatar: avatarUrl
        };
        
        // 关闭对话框
        closeEditProfileModal();
      } catch (error) {
        console.error('更新个人资料失败:', error);
        alert('更新个人资料失败，请重试');
      } finally {
        isSubmitting.value = false;
      }
    };
    
    const createTrip = () => {
      router.push('/create-trip');
    };
    
    const createDiary = () => {
      router.push('/create-diary');
    };
    
    const viewTripDetail = (tripId) => {
      router.push(`/trip/${tripId}`);
    };
    
    const viewDiaryDetail = (diaryId) => {
      router.push(`/diary/${diaryId}`);
    };
    
    const formatDate = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
    };
    
    return {
      isLoading,
      isLoggedIn,
      isSubmitting,
      userProfile,
      trips,
      diaries,
      activeTab,
      avatarPreview,
      profileForm,
      login,
      openEditProfileModal,
      closeEditProfileModal,
      handleAvatarChange,
      updateProfile,
      createTrip,
      createDiary,
      viewTripDetail,
      viewDiaryDetail,
      formatDate
    };
  }
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
<template>
  <div 
    class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-200 hover:shadow-lg cursor-pointer"
    @click="$emit('click')"
  >
    <!-- 封面图片 -->
    <div class="h-48 bg-gray-200 overflow-hidden">
      <img 
        v-if="image" 
        :src="image" 
        :alt="title"
        class="w-full h-full object-cover"
      >
      <div v-else class="w-full h-full flex items-center justify-center bg-gray-100 text-gray-400">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="p-4">
      <h3 class="text-lg font-semibold text-gray-800 mb-1 line-clamp-1">{{ title }}</h3>
      <p class="text-sm text-gray-500 mb-2">{{ date }}</p>
      <p class="text-gray-600 line-clamp-2">{{ summary }}</p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: true
    },
    date: {
      type: String,
      required: true
    },
    summary: {
      type: String,
      required: true
    },
    image: {
      type: String,
      default: ''
    }
  }
}
</script>
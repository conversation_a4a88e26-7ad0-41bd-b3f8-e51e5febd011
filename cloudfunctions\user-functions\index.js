const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 获取用户信息
 */
exports.main = async (event, context) => {
  const { userId } = event

  try {
    // 查询用户信息
    const { data } = await db.collection('users')
      .where({
        _openid: userId
      })
      .get()

    if (data.length === 0) {
      // 如果用户不存在，创建新用户记录
      const newUser = {
        _openid: userId,
        name: '新用户',
        avatar: '',
        bio: '',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const { _id } = await db.collection('users').add({
        data: newUser
      })

      return {
        code: 200,
        data: {
          ...newUser,
          _id
        }
      }
    }

    return {
      code: 200,
      data: data[0]
    }
  } catch (err) {
    console.error('获取用户信息失败:', err)
    return {
      code: 500,
      message: '获取用户信息失败'
    }
  }
}
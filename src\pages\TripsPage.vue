<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">我的旅行计划</h1>
      <button v-if="isLoggedIn" class="btn btn-primary" @click="openCreateModal">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        新建旅行计划
      </button>
    </div>
    
    <div v-if="isLoading" class="flex justify-center py-12">
      <span class="loading loading-spinner loading-lg"></span>
    </div>
    
    <div v-else-if="!isLoggedIn" class="card bg-base-200 p-6 max-w-md mx-auto">
      <h2 class="text-xl font-semibold mb-4">请登录</h2>
      <p class="mb-4">登录后可以创建和管理您的旅行计划。</p>
      <button class="btn btn-primary" @click="login">登录/注册</button>
    </div>
    
    <div v-else-if="trips.length === 0" class="card bg-base-200 p-8 text-center">
      <div class="mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
        </svg>
      </div>
      <h2 class="text-xl font-semibold mb-2">暂无旅行计划</h2>
      <p class="mb-4 opacity-70">创建您的第一个旅行计划，开始规划您的旅程吧！</p>
      <button class="btn btn-primary" @click="openCreateModal">创建旅行计划</button>
    </div>
    
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="trip in trips" :key="trip._id" class="card bg-base-200 shadow-xl">
        <figure class="h-48 bg-gray-300">
          <img v-if="trip.coverImage" :src="trip.coverImage" alt="旅行封面" class="w-full h-full object-cover" />
          <div v-else class="w-full h-full flex items-center justify-center bg-primary bg-opacity-20">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
            </svg>
          </div>
        </figure>
        <div class="card-body">
          <div class="flex justify-between items-start">
            <h2 class="card-title">{{ trip.title }}</h2>
            <div class="badge badge-outline" :class="trip.isPublic ? 'badge-success' : 'badge-secondary'">
              {{ trip.isPublic ? '公开' : '私密' }}
            </div>
          </div>
          <p class="opacity-70 line-clamp-2">{{ trip.description || '暂无描述' }}</p>
          <div class="flex items-center text-sm opacity-70 mt-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            {{ formatDate(trip.startDate) }} - {{ formatDate(trip.endDate) }}
          </div>
          <div class="flex flex-wrap gap-1 mt-2">
            <div v-for="(destination, index) in trip.destinations.slice(0, 3)" :key="index" class="badge badge-primary">
              {{ destination }}
            </div>
            <div v-if="trip.destinations.length > 3" class="badge badge-outline">
              +{{ trip.destinations.length - 3 }}
            </div>
          </div>
          <div class="card-actions justify-end mt-4">
            <button class="btn btn-sm btn-outline" @click="editTrip(trip)">编辑</button>
            <button class="btn btn-sm btn-primary" @click="viewTripDetail(trip._id)">查看详情</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 创建/编辑旅行计划对话框 -->
    <dialog id="trip_modal" class="modal">
      <div class="modal-box max-w-2xl">
        <h3 class="font-bold text-lg mb-4">{{ isEditing ? '编辑旅行计划' : '创建旅行计划' }}</h3>
        <form @submit.prevent="saveTrip">
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">旅行标题</span>
            </label>
            <input type="text" v-model="tripForm.title" class="input input-bordered" required />
          </div>
          
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">旅行描述</span>
            </label>
            <textarea v-model="tripForm.description" class="textarea textarea-bordered" rows="3"></textarea>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">开始日期</span>
              </label>
              <input type="date" v-model="tripForm.startDate" class="input input-bordered" required />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">结束日期</span>
              </label>
              <input type="date" v-model="tripForm.endDate" class="input input-bordered" required />
            </div>
          </div>
          
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">目的地</span>
              <span class="label-text-alt">最多添加10个目的地</span>
            </label>
            <div class="flex flex-wrap gap-2 mb-2">
              <div v-for="(destination, index) in tripForm.destinations" :key="index" class="badge badge-primary gap-1">
                {{ destination }}
                <button type="button" @click="removeDestination(index)">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            <div class="join">
              <input type="text" v-model="newDestination" class="input input-bordered join-item flex-1" placeholder="输入目的地名称" />
              <button type="button" class="btn join-item" @click="addDestination" :disabled="tripForm.destinations.length >= 10 || !newDestination">添加</button>
            </div>
          </div>
          
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">封面图片</span>
            </label>
            <div class="flex items-center space-x-4">
              <div class="w-24 h-24 bg-gray-200 rounded-lg overflow-hidden">
                <img v-if="tripForm.coverImage" :src="tripForm.coverImage" alt="封面预览" class="w-full h-full object-cover" />
                <div v-else class="w-full h-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
              <input type="file" accept="image/*" class="file-input file-input-bordered file-input-sm w-full max-w-xs" @change="handleCoverImageChange" />
            </div>
          </div>
          
          <div class="form-control mb-4">
            <label class="label cursor-pointer">
              <span class="label-text">公开此旅行计划</span>
              <input type="checkbox" v-model="tripForm.isPublic" class="toggle toggle-primary" />
            </label>
            <p class="text-xs opacity-70">公开的旅行计划可以被其他用户查看</p>
          </div>
          
          <div class="modal-action">
            <button type="button" class="btn" @click="closeTripModal">取消</button>
            <button v-if="isEditing" type="button" class="btn btn-error" @click="confirmDeleteTrip">删除</button>
            <button type="submit" class="btn btn-primary">保存</button>
          </div>
        </form>
      </div>
      <form method="dialog" class="modal-backdrop">
        <button>关闭</button>
      </form>
    </dialog>
    
    <!-- 删除确认对话框 -->
    <dialog id="delete_confirm_modal" class="modal">
      <div class="modal-box">
        <h3 class="font-bold text-lg">确认删除</h3>
        <p class="py-4">您确定要删除这个旅行计划吗？此操作无法撤销。</p>
        <div class="modal-action">
          <button class="btn" @click="closeDeleteModal">取消</button>
          <button class="btn btn-error" @click="deleteTrip">删除</button>
        </div>
      </div>
      <form method="dialog" class="modal-backdrop">
        <button>关闭</button>
      </form>
    </dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { auth, db, storage } from '../utils/cloudbase';

const router = useRouter();
const isLoading = ref(true);
const trips = ref([]);
const isEditing = ref(false);
const currentTripId = ref(null);
const newDestination = ref('');

const tripForm = ref({
  title: '',
  description: '',
  startDate: '',
  endDate: '',
  destinations: [],
  coverImage: '',
  isPublic: false
});

const isLoggedIn = computed(() => auth.hasLoginState());

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
};

// 获取旅行计划列表
const fetchTrips = async () => {
  if (!isLoggedIn.value) {
    isLoading.value = false;
    return;
  }
  
  try {
    const userId = auth.currentUser.uid;
    const result = await db.collection('trips')
      .where({ userId: userId })
      .orderBy('createdAt', 'desc')
      .get();
    
    if (result.data) {
      trips.value = result.data;
    }
  } catch (error) {
    console.error('获取旅行计划失败:', error);
  } finally {
    isLoading.value = false;
  }
};

// 登录
const login = async () => {
  try {
    await auth.signInWithRedirect();
    await fetchTrips();
  } catch (error) {
    console.error('登录失败:', error);
  }
};

// 打开创建旅行计划对话框
const openCreateModal = () => {
  isEditing.value = false;
  currentTripId.value = null;
  tripForm.value = {
    title: '',
    description: '',
    startDate: formatDate(new Date()),
    endDate: formatDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)), // 默认一周后
    destinations: [],
    coverImage: '',
    isPublic: false
  };
  
  const modal = document.getElementById('trip_modal');
  modal.showModal();
};

// 编辑旅行计划
const editTrip = (trip) => {
  isEditing.value = true;
  currentTripId.value = trip._id;
  
  tripForm.value = {
    title: trip.title,
    description: trip.description || '',
    startDate: formatDate(trip.startDate),
    endDate: formatDate(trip.endDate),
    destinations: [...trip.destinations],
    coverImage: trip.coverImage || '',
    isPublic: trip.isPublic || false
  };
  
  const modal = document.getElementById('trip_modal');
  modal.showModal();
};

// 关闭旅行计划对话框
const closeTripModal = () => {
  const modal = document.getElementById('trip_modal');
  modal.close();
};

// 添加目的地
const addDestination = () => {
  if (newDestination.value.trim() && tripForm.value.destinations.length < 10) {
    tripForm.value.destinations.push(newDestination.value.trim());
    newDestination.value = '';
  }
};

// 移除目的地
const removeDestination = (index) => {
  tripForm.value.destinations.splice(index, 1);
};

// 处理封面图片上传
const handleCoverImageChange = async (event) => {
  const file = event.target.files[0];
  if (!file) return;
  
  try {
    const path = `trip-covers/${Date.now()}-${file.name}`;
    const result = await storage.uploadFile({
      cloudPath: path,
      filePath: file
    });
    
    const fileID = result.fileID;
    const tempUrl = await storage.getTempFileURL({
      fileList: [fileID]
    });
    
    if (tempUrl.fileList && tempUrl.fileList[0]) {
      tripForm.value.coverImage = tempUrl.fileList[0].tempFileURL;
    }
  } catch (error) {
    console.error('上传图片失败:', error);
    alert('上传图片失败，请重试');
  }
};

// 保存旅行计划
const saveTrip = async () => {
  try {
    // 验证日期
    if (new Date(tripForm.value.endDate) < new Date(tripForm.value.startDate)) {
      alert('结束日期不能早于开始日期');
      return;
    }
    
    const tripData = {
      title: tripForm.value.title,
      description: tripForm.value.description,
      startDate: tripForm.value.startDate,
      endDate: tripForm.value.endDate,
      destinations: tripForm.value.destinations,
      coverImage: tripForm.value.coverImage,
      isPublic: tripForm.value.isPublic,
      updatedAt: new Date()
    };
    
    if (isEditing.value && currentTripId.value) {
      // 更新旅行计划
      await db.collection('trips').doc(currentTripId.value).update(tripData);
      
      // 更新本地数据
      const index = trips.value.findIndex(t => t._id === currentTripId.value);
      if (index !== -1) {
        trips.value[index] = { ...trips.value[index], ...tripData };
      }
    } else {
      // 创建新旅行计划
      tripData.userId = auth.currentUser.uid;
      tripData.createdAt = new Date();
      
      const result = await db.collection('trips').add(tripData);
      
      // 添加到本地数据
      trips.value.unshift({
        _id: result.id,
        ...tripData
      });
    }
    
    // 关闭对话框
    closeTripModal();
  } catch (error) {
    console.error('保存旅行计划失败:', error);
    alert('保存失败，请重试');
  }
};

// 确认删除旅行计划
const confirmDeleteTrip = () => {
  const modal = document.getElementById('delete_confirm_modal');
  modal.showModal();
};

// 关闭删除确认对话框
const closeDeleteModal = () => {
  const modal = document.getElementById('delete_confirm_modal');
  modal.close();
};

// 删除旅行计划
const deleteTrip = async () => {
  if (!currentTripId.value) return;
  
  try {
    await db.collection('trips').doc(currentTripId.value).remove();
    
    // 更新本地数据
    trips.value = trips.value.filter(t => t._id !== currentTripId.value);
    
    // 关闭对话框
    closeDeleteModal();
    closeTripModal();
  } catch (error) {
    console.error('删除旅行计划失败:', error);
    alert('删除失败，请重试');
  }
};

// 查看旅行计划详情
const viewTripDetail = (tripId) => {
  router.push(`/trip/${tripId}`);
};

// 页面加载时获取数据
onMounted(async () => {
  await fetchTrips();
});
</script>

<style scoped>
.card {
  transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
</style>
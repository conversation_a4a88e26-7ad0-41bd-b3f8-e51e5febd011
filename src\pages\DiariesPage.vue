<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">我的旅行日记</h1>
      <button v-if="isLoggedIn" class="btn btn-primary" @click="openCreateModal">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        写新日记
      </button>
    </div>
    
    <div v-if="isLoading" class="flex justify-center py-12">
      <span class="loading loading-spinner loading-lg"></span>
    </div>
    
    <div v-else-if="!isLoggedIn" class="card bg-base-200 p-6 max-w-md mx-auto">
      <h2 class="text-xl font-semibold mb-4">请登录</h2>
      <p class="mb-4">登录后可以记录和分享您的旅行经历。</p>
      <button class="btn btn-primary" @click="login">登录/注册</button>
    </div>
    
    <div v-else-if="diaries.length === 0" class="card bg-base-200 p-8 text-center">
      <div class="mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
        </svg>
      </div>
      <h2 class="text-xl font-semibold mb-2">暂无旅行日记</h2>
      <p class="mb-4 opacity-70">记录您的旅行经历，分享您的故事和照片！</p>
      <button class="btn btn-primary" @click="openCreateModal">写第一篇日记</button>
    </div>
    
    <div v-else>
      <!-- 日记过滤器 -->
      <div class="flex flex-wrap gap-2 mb-6">
        <button 
          class="btn btn-sm" 
          :class="selectedTrip === null ? 'btn-primary' : 'btn-outline'"
          @click="filterByTrip(null)"
        >
          全部日记
        </button>
        <button 
          v-for="trip in trips" 
          :key="trip._id" 
          class="btn btn-sm" 
          :class="selectedTrip === trip._id ? 'btn-primary' : 'btn-outline'"
          @click="filterByTrip(trip._id)"
        >
          {{ trip.title }}
        </button>
      </div>
      
      <!-- 日记列表 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div v-for="diary in filteredDiaries" :key="diary._id" class="card bg-base-200 shadow-xl">
          <figure class="h-48 bg-gray-300">
            <img v-if="diary.coverImage" :src="diary.coverImage" alt="日记封面" class="w-full h-full object-cover" />
            <div v-else class="w-full h-full flex items-center justify-center bg-primary bg-opacity-20">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
              </svg>
            </div>
          </figure>
          <div class="card-body">
            <div class="flex justify-between items-start">
              <h2 class="card-title">{{ diary.title }}</h2>
              <div class="badge badge-outline" :class="diary.isPublic ? 'badge-success' : 'badge-secondary'">
                {{ diary.isPublic ? '公开' : '私密' }}
              </div>
            </div>
            <p class="opacity-70 line-clamp-2">{{ diary.summary || '暂无摘要' }}</p>
            <div class="flex items-center text-sm opacity-70 mt-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              {{ formatDate(diary.date) }}
            </div>
            <div class="flex items-center text-sm opacity-70 mt-1">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              {{ diary.location || '未知地点' }}
            </div>
            <div class="flex flex-wrap gap-1 mt-2">
              <div v-for="(tag, index) in diary.tags.slice(0, 3)" :key="index" class="badge badge-primary">
                {{ tag }}
              </div>
              <div v-if="diary.tags.length > 3" class="badge badge-outline">
                +{{ diary.tags.length - 3 }}
              </div>
            </div>
            <div class="card-actions justify-end mt-4">
              <button class="btn btn-sm btn-outline" @click="editDiary(diary)">编辑</button>
              <button class="btn btn-sm btn-primary" @click="viewDiaryDetail(diary._id)">阅读全文</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 创建/编辑日记对话框 -->
    <dialog id="diary_modal" class="modal">
      <div class="modal-box max-w-4xl">
        <h3 class="font-bold text-lg mb-4">{{ isEditing ? '编辑旅行日记' : '创建旅行日记' }}</h3>
        <form @submit.prevent="saveDiary">
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">日记标题</span>
            </label>
            <input type="text" v-model="diaryForm.title" class="input input-bordered" required />
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">日期</span>
              </label>
              <input type="date" v-model="diaryForm.date" class="input input-bordered" required />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">地点</span>
              </label>
              <input type="text" v-model="diaryForm.location" class="input input-bordered" placeholder="例如：巴黎、东京" />
            </div>
          </div>
          
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">关联旅行计划</span>
            </label>
            <select v-model="diaryForm.tripId" class="select select-bordered w-full">
              <option value="">不关联旅行计划</option>
              <option v-for="trip in trips" :key="trip._id" :value="trip._id">{{ trip.title }}</option>
            </select>
          </div>
          
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">日记摘要</span>
            </label>
            <textarea v-model="diaryForm.summary" class="textarea textarea-bordered" rows="2" placeholder="简短描述这篇日记的内容"></textarea>
          </div>
          
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">日记内容</span>
            </label>
            <textarea v-model="diaryForm.content" class="textarea textarea-bordered" rows="8" required placeholder="记录您的旅行经历、感受和故事..."></textarea>
          </div>
          
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">标签</span>
              <span class="label-text-alt">最多添加5个标签</span>
            </label>
            <div class="flex flex-wrap gap-2 mb-2">
              <div v-for="(tag, index) in diaryForm.tags" :key="index" class="badge badge-primary gap-1">
                {{ tag }}
                <button type="button" @click="removeTag(index)">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            <div class="join">
              <input type="text" v-model="newTag" class="input input-bordered join-item flex-1" placeholder="输入标签" />
              <button type="button" class="btn join-item" @click="addTag" :disabled="diaryForm.tags.length >= 5 || !newTag">添加</button>
            </div>
          </div>
          
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">封面图片</span>
            </label>
            <div class="flex items-center space-x-4">
              <div class="w-24 h-24 bg-gray-200 rounded-lg overflow-hidden">
                <img v-if="diaryForm.coverImage" :src="diaryForm.coverImage" alt="封面预览" class="w-full h-full object-cover" />
                <div v-else class="w-full h-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
              <input type="file" accept="image/*" class="file-input file-input-bordered file-input-sm w-full max-w-xs" @change="handleCoverImageChange" />
            </div>
          </div>
          
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">照片集</span>
            </label>
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 mb-2">
              <div v-for="(photo, index) in diaryForm.photos" :key="index" class="relative aspect-square bg-gray-200 rounded-lg overflow-hidden">
                <img :src="photo.url" alt="旅行照片" class="w-full h-full object-cover" />
                <button type="button" class="absolute top-1 right-1 btn btn-circle btn-xs btn-error" @click="removePhoto(index)">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div v-if="diaryForm.photos.length < 10" class="aspect-square flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg cursor-pointer" @click="triggerPhotoUpload">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                <input type="file" ref="photoInput" accept="image/*" class="hidden" @change="handlePhotoChange" />
              </div>
            </div>
          </div>
          
          <div class="form-control mb-4">
            <div class="flex items-center">
              <input type="checkbox" v-model="diaryForm.isPublic" class="checkbox checkbox-primary" />
              <label class="label cursor-pointer">
                <span class="label-text ml-2">公开日记</span>
              </label>
            </div>
            <div class="text-xs opacity-70 mt-1">公开的日记可以被其他用户查看和评论</div>
          </div>
          
          <div class="modal-action">
            <button type="button" class="btn" @click="closeModal">取消</button>
            <button type="submit" class="btn btn-primary" :disabled="isSaving">
              <span v-if="isSaving" class="loading loading-spinner loading-xs mr-1"></span>
              {{ isEditing ? '保存修改' : '创建日记' }}
            </button>
          </div>
        </form>
      </div>
    </dialog>
    
    <!-- 删除确认对话框 -->
    <dialog id="delete_confirm_modal" class="modal">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">确认删除</h3>
        <p>您确定要删除这篇日记吗？此操作无法撤销。</p>
        <div class="modal-action">
          <button class="btn" @click="closeDeleteModal">取消</button>
          <button class="btn btn-error" @click="deleteDiary" :disabled="isDeleting">
            <span v-if="isDeleting" class="loading loading-spinner loading-xs mr-1"></span>
            删除
          </button>
        </div>
      </div>
    </dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { app, auth, db, checkLogin } from '../utils/cloudbase';

export default {
  name: 'DiariesPage',
  setup() {
    const router = useRouter();
    
    // 状态变量
    const isLoggedIn = ref(false);
    const isLoading = ref(true);
    const isSaving = ref(false);
    const isDeleting = ref(false);
    const isEditing = ref(false);
    const diaries = ref([]);
    const trips = ref([]);
    const selectedTrip = ref(null);
    const currentUserId = ref(null);
    
    // 表单数据
    const diaryForm = ref({
      _id: null,
      title: '',
      date: new Date().toISOString().split('T')[0],
      location: '',
      tripId: '',
      summary: '',
      content: '',
      tags: [],
      coverImage: null,
      photos: [],
      isPublic: true
    });
    
    const newTag = ref('');
    const selectedDiaryId = ref(null);
    
    // DOM引用
    const photoInput = ref(null);
    
    // 计算属性
    const filteredDiaries = computed(() => {
      if (selectedTrip.value === null) {
        return diaries.value;
      }
      return diaries.value.filter(diary => diary.tripId === selectedTrip.value);
    });
    
    // 生命周期钩子
    onMounted(async () => {
      await checkLoginStatus();
      if (isLoggedIn.value) {
        await Promise.all([
          fetchDiaries(),
          fetchTrips()
        ]);
      }
      isLoading.value = false;
    });
    
    // 方法
    const checkLoginStatus = async () => {
      try {
        await checkLogin();
        const loginState = await auth.getLoginState();
        
        isLoggedIn.value = loginState && loginState.isLoggedIn;
        if (isLoggedIn.value) {
          currentUserId.value = loginState.user.uid;
        }
      } catch (error) {
        console.error('检查登录状态失败:', error);
        isLoggedIn.value = false;
      }
    };
    
    const login = async () => {
      try {
        await auth.toDefaultLoginPage();
      } catch (error) {
        console.error('登录失败:', error);
      }
    };
    
    const fetchDiaries = async () => {
      try {
        // 验证登录状态
        const loginState = await auth.getLoginState();
        if (!loginState?.isLoggedIn) {
          throw new Error('未登录');
        }

        // 只查询当前用户的日记
        const { data } = await db.collection('diaries')
          .where({
            userId: loginState.user.uid
          })
          .orderBy('date', 'desc')
          .get();
        
        diaries.value = data;
      } catch (error) {
        console.error('获取日记列表失败:', error);
        if (error.message === '未登录') {
          router.push('/auth');
        }
      }
    };
    
    const fetchTrips = async () => {
      try {
        // 查询当前用户的旅行计划
        const { data } = await db.collection('trips')
          .where({
            _openid: currentUserId.value
          })
          .orderBy('startDate', 'desc')
          .get();
        
        trips.value = data;
      } catch (error) {
        console.error('获取旅行计划列表失败:', error);
      }
    };
    
    const filterByTrip = (tripId) => {
      selectedTrip.value = tripId;
    };
    
    const openCreateModal = async () => {
      try {
        console.log('开始创建日记流程');
        
        // 检查登录状态
        const loginState = await auth.getLoginState();
        console.log('当前登录状态:', loginState);
        
        if (!loginState || !loginState.isLoggedIn) {
          console.log('尝试匿名登录...');
          const loginResult = await auth.signInAnonymously();
          console.log('匿名登录结果:', loginResult);
          isLoggedIn.value = true;
        }

        // 验证数据库访问权限
        try {
          const db = app.database();
          const testQuery = await db.collection('diaries').limit(1).get();
          console.log('数据库访问测试成功:', testQuery);
        } catch (dbError) {
          console.error('数据库访问错误:', dbError);
          throw new Error('无法访问数据库，请检查权限配置');
        }

        // 重置表单
        diaryForm.value = {
          _id: null,
          title: '',
          date: new Date().toISOString().split('T')[0],
          location: '',
          tripId: '',
          summary: '',
          content: '',
          tags: [],
          coverImage: null,
          photos: [],
          isPublic: true
        };
        newTag.value = '';
        isEditing.value = false;
        
        // 打开模态框
        const modal = document.getElementById('diary_modal');
        if (modal) {
          modal.showModal();
          console.log('模态框已打开');
        } else {
          throw new Error('找不到模态框元素');
        }
      } catch (error) {
        console.error('创建日记失败:', error);
        alert(`创建日记失败: ${error.message}`);
        
        // 尝试跳转登录页
        try {
          await auth.toDefaultLoginPage();
        } catch (loginError) {
          console.error('跳转登录页失败:', loginError);
        }
      }
    };
    
    const editDiary = (diary) => {
      // 填充表单
      diaryForm.value = {
        _id: diary._id,
        title: diary.title,
        date: diary.date.split('T')[0],
        location: diary.location || '',
        tripId: diary.tripId || '',
        summary: diary.summary || '',
        content: diary.content,
        tags: [...(diary.tags || [])],
        coverImage: diary.coverImage,
        photos: [...(diary.photos || [])],
        isPublic: diary.isPublic !== false
      };
      newTag.value = '';
      isEditing.value = true;
      
      // 打开模态框
      const modal = document.getElementById('diary_modal');
      modal.showModal();
    };
    
    const closeModal = () => {
      const modal = document.getElementById('diary_modal');
      modal.close();
    };
    
    const saveDiary = async () => {
      try {
        isSaving.value = true;
        
        const diaryData = {
          title: diaryForm.value.title,
          date: diaryForm.value.date,
          location: diaryForm.value.location,
          tripId: diaryForm.value.tripId || null,
          summary: diaryForm.value.summary,
          content: diaryForm.value.content,
          tags: diaryForm.value.tags,
          coverImage: diaryForm.value.coverImage,
          photos: diaryForm.value.photos,
          isPublic: diaryForm.value.isPublic,
          updatedAt: new Date()
        };
        
        if (isEditing.value) {
          // 更新日记
          await db.collection('diaries').doc(diaryForm.value._id).update(diaryData);
          
          // 更新本地数据
          const index = diaries.value.findIndex(d => d._id === diaryForm.value._id);
          if (index !== -1) {
            diaries.value[index] = { ...diaries.value[index], ...diaryData };
          }
        } else {
          // 创建日记
          diaryData.createdAt = new Date();
          
          const { id } = await db.collection('diaries').add(diaryData);
          
          // 添加到本地数据
          diaries.value.unshift({
            _id: id,
            ...diaryData,
            _openid: currentUserId.value
          });
        }
        
        // 关闭模态框
        closeModal();
      } catch (error) {
        console.error('保存日记失败:', error);
        alert('保存失败，请重试');
      } finally {
        isSaving.value = false;
      }
    };
    
    const confirmDeleteDiary = (diary) => {
      selectedDiaryId.value = diary._id;
      
      // 打开确认对话框
      const modal = document.getElementById('delete_confirm_modal');
      modal.showModal();
    };
    
    const closeDeleteModal = () => {
      const modal = document.getElementById('delete_confirm_modal');
      modal.close();
      selectedDiaryId.value = null;
    };
    
    const deleteDiary = async () => {
      if (!selectedDiaryId.value) return;
      
      try {
        isDeleting.value = true;
        
        // 删除日记
        await db.collection('diaries').doc(selectedDiaryId.value).remove();
        
        // 更新本地数据
        diaries.value = diaries.value.filter(d => d._id !== selectedDiaryId.value);
        
        // 关闭确认对话框
        closeDeleteModal();
      } catch (error) {
        console.error('删除日记失败:', error);
        alert('删除失败，请重试');
      } finally {
        isDeleting.value = false;
      }
    };
    
    const addTag = () => {
      if (newTag.value.trim() && diaryForm.value.tags.length < 5) {
        diaryForm.value.tags.push(newTag.value.trim());
        newTag.value = '';
      }
    };
    
    const removeTag = (index) => {
      diaryForm.value.tags.splice(index, 1);
    };
    
    const handleCoverImageChange = async (event) => {
      const file = event.target.files[0];
      if (!file) return;
      
      try {
        // 上传文件
        const result = await app.uploadFile({
          cloudPath: `diary-covers/${Date.now()}-${file.name}`,
          filePath: file
        });
        
        // 获取文件访问链接
        const fileList = [{ fileID: result.fileID }];
        const { fileList: resultList } = await app.getTempFileURL({ fileList });
        
        diaryForm.value.coverImage = resultList[0].tempFileURL;
      } catch (error) {
        console.error('上传封面图片失败:', error);
        alert('上传图片失败，请重试');
      }
    };
    
    const triggerPhotoUpload = () => {
      photoInput.value.click();
    };
    
    const handlePhotoChange = async (event) => {
      const file = event.target.files[0];
      if (!file) return;
      
      try {
        // 上传文件
        const result = await app.uploadFile({
          cloudPath: `diary-photos/${Date.now()}-${file.name}`,
          filePath: file
        });
        
        // 获取文件访问链接
        const fileList = [{ fileID: result.fileID }];
        const { fileList: resultList } = await app.getTempFileURL({ fileList });
        
        diaryForm.value.photos.push({
          id: Date.now().toString(),
          url: resultList[0].tempFileURL,
          fileID: result.fileID
        });
      } catch (error) {
        console.error('上传照片失败:', error);
        alert('上传照片失败，请重试');
      }
    };
    
    const removePhoto = (index) => {
      diaryForm.value.photos.splice(index, 1);
    };
    
    const viewDiaryDetail = (diary) => {
      router.push(`/diary/${diary._id}`);
    };
    
    return {
      isLoggedIn,
      isLoading,
      isSaving,
      isDeleting,
      isEditing,
      diaries,
      trips,
      selectedTrip,
      diaryForm,
      newTag,
      photoInput,
      filteredDiaries,
      
      login,
      filterByTrip,
      openCreateModal,
      editDiary,
      closeModal,
      saveDiary,
      confirmDeleteDiary,
      closeDeleteModal,
      deleteDiary,
      addTag,
      removeTag,
      handleCoverImageChange,
      triggerPhotoUpload,
      handlePhotoChange,
      removePhoto,
      viewDiaryDetail
    };
  }
};
</script>

<style scoped>
.diary-card {
  transition: transform 0.2s, box-shadow 0.2s;
}

.diary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.diary-cover {
  height: 200px;
  object-fit: cover;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  line-height: 1;
  background-color: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.tag-close {
  margin-left: 0.25rem;
  width: 1rem;
  height: 1rem;
  border-radius: 9999px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.tag-close:hover {
  background-color: rgba(79, 70, 229, 0.2);
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0.5rem;
}

.photo-item {
  position: relative;
  aspect-ratio: 1;
}

.photo-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0.375rem;
}

.photo-remove {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.photo-remove:hover {
  background-color: rgba(0, 0, 0, 0.7);
}
</style>
const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event
  const wxContext = cloud.getWXContext()
  const openId = wxContext.OPENID

  switch (action) {
    case 'createComment':
      return await createComment(data, openId)
    case 'deleteComment':
      return await deleteComment(data, openId)
    case 'getCommentList':
      return await getCommentList(data)
    default:
      return {
        code: 404,
        message: '未找到对应的操作'
      }
  }
}

/**
 * 创建评论
 */
async function createComment(data, openId) {
  try {
    const { diaryId, content } = data
    
    // 检查日记是否存在
    const diary = await db.collection('diaries').doc(diaryId).get()
    if (!diary.data) {
      return {
        code: 404,
        message: '日记不存在'
      }
    }

    // 获取用户信息
    const { data: users } = await db.collection('users')
      .where({
        _openid: openId
      })
      .get()
    
    const user = users[0] || {}
    
    const commentData = {
      diaryId,
      content,
      _openid: openId,
      authorName: user.name || '匿名用户',
      authorAvatar: user.avatar || '',
      createdAt: db.serverDate()
    }

    const result = await db.collection('comments').add({
      data: commentData
    })

    // 更新日记的评论数
    await db.collection('diaries').doc(diaryId).update({
      data: {
        comments: _.inc(1),
        updatedAt: db.serverDate()
      }
    })

    return {
      code: 200,
      data: {
        _id: result._id,
        ...commentData
      }
    }
  } catch (err) {
    console.error('创建评论失败:', err)
    return {
      code: 500,
      message: '创建评论失败'
    }
  }
}

/**
 * 删除评论
 */
async function deleteComment(data, openId) {
  try {
    const { commentId } = data
    
    // 获取评论信息
    const comment = await db.collection('comments').doc(commentId).get()
    
    if (!comment.data) {
      return {
        code: 404,
        message: '评论不存在'
      }
    }
    
    // 检查是否是评论的创建者
    if (comment.data._openid !== openId) {
      return {
        code: 403,
        message: '无权限删除此评论'
      }
    }

    // 删除评论
    await db.collection('comments').doc(commentId).remove()

    // 更新日记的评论数
    await db.collection('diaries').doc(comment.data.diaryId).update({
      data: {
        comments: _.inc(-1),
        updatedAt: db.serverDate()
      }
    })

    return {
      code: 200,
      message: '删除评论成功'
    }
  } catch (err) {
    console.error('删除评论失败:', err)
    return {
      code: 500,
      message: '删除评论失败'
    }
  }
}

/**
 * 获取评论列表
 */
async function getCommentList(data) {
  try {
    const { diaryId, page = 1, pageSize = 10 } = data
    const skip = (page - 1) * pageSize
    
    // 检查日记是否存在
    const diary = await db.collection('diaries').doc(diaryId).get()
    if (!diary.data) {
      return {
        code: 404,
        message: '日记不存在'
      }
    }
    
    // 获取总数
    const countResult = await db.collection('comments')
      .where({
        diaryId
      })
      .count()
    
    // 获取分页数据
    const { data: comments } = await db.collection('comments')
      .where({
        diaryId
      })
      .orderBy('createdAt', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get()
    
    return {
      code: 200,
      data: {
        list: comments,
        total: countResult.total,
        page,
        pageSize
      }
    }
  } catch (err) {
    console.error('获取评论列表失败:', err)
    return {
      code: 500,
      message: '获取评论列表失败'
    }
  }
}